interface GlobalApp {
  id: number;
  isOnline: boolean;
  isTest: boolean;
  isLocal: boolean;
  domain: { [key: string]: any };
  appList: Array<{ name: string; url: string }>;
  enName: string;
  [key: string]: any;
}

declare const ConfigJson: Record<string, any>;

declare const APP: GlobalApp;

declare module '*.svg';
declare module '*.png';
declare module '*.jpg';
declare module '*.jpeg';
declare module '*.gif';
declare module '*.bmp';
declare module '*.tiff';
