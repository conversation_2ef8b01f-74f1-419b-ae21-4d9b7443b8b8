const path = require('path');

module.exports = (env, argv) => {
  const isProduction = argv.mode === 'production';

  return {
    mode: argv.mode || 'development', // 从命令行参数获取 mode，默认为 development
    entry: './src/background/main.ts',
    output: {
      path: path.resolve(__dirname, 'public/background'), // 修改输出路径
      filename: 'background.umd.js',
      library: 'BackgroundService',
      libraryTarget: 'umd',
      globalObject: 'this',
      umdNamedDefine: true
    },
    module: {
      rules: [
        {
          test: /\\.tsx?$/,
          exclude: /node_modules/,
          use: {
            loader: 'babel-loader',
            options: {
              // Babel 配置会从 babel.config.js 文件加载
              // 请确保 babel.config.js 中的 presets 包含 @babel/preset-typescript
              // 或者 @vue/cli-plugin-babel/preset 已经正确处理了 TypeScript
            }
          }
        },
        {
          test: /\\.js$/,
          exclude: /node_modules/,
          use: {
            loader: 'babel-loader'
            // Babel 配置会从 babel.config.js 文件加载
          }
        }
      ]
    },
    resolve: {
      extensions: ['.ts', '.tsx', '.js']
    },
    devtool: isProduction ? false : 'source-map', // 非生产环境开启 source-map
    optimization: {
      minimize: isProduction // 生产环境压缩，开发环境不压缩
    },

    watch: !isProduction, // 开发模式下开启 watch

    watchOptions: {
      ignored: /node_modules/ // 忽略 node_modules 目录的变化
    }
  };
};
