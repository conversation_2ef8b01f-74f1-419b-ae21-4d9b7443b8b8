const baseSearch = '?_paasFramework=true';

const testMap = {
  volvo: 'https://admin.mucang.cn/volvo-jiaxiao.ttt.mucang.cn',
  'after-sales': 'https://admin.mucang.cn/jiaxiao-admin.ttt.mucang.cn',
  'jiaxiao-vip': 'https://admin.mucang.cn/jiaxiao-vip.ttt.mucang.cn',
  jiakao: 'https://admin.mucang.cn/test.jiaxiao.jiakaobaodian.com',
  'jiaxiao-lead-filter': 'https://admin.mucang.cn/jiaxiao-lead-filter.ttt.mucang.cn',
  'jiaxiao-report': 'https://admin.mucang.cn/jiaxiao-report.ttt.mucang.cn',
  'order-jiaxiao': 'https://admin.mucang.cn/order-jiaxiao.ttt.mucang.cn',
  'order-coach': 'https://admin.mucang.cn/order-coach.ttt.mucang.cn',
  swallow: 'https://admin.mucang.cn/swallow.ttt.mucang.cn'
};

// 生产环境 appName 对应的地址
const prodMap = {
  volvo: 'https://admin.mucang.cn/volvo-jiaxiao.kakamobi.cn',
  'after-sales': 'https://admin.mucang.cn/jiaxiao-admin.jiakaobaodian.com',
  'jiaxiao-vip': 'https://admin.mucang.cn/jiaxiao-vip.kakamobi.cn',
  jiakao: 'https://admin.mucang.cn/jiaxiao.jiakaobaodian.com',
  'jiaxiao-lead-filter': 'https://admin.mucang.cn/jiaxiao-lead-filter.kakamobil.cn',
  'jiaxiao-report': 'https://admin.mucang.cn/jiaxiao-report.kakamobi.cn',
  'order-jiaxiao': 'https://admin.mucang.cn/order-jiaxiao.kakamobi.cn',
  'order-coach': 'https://admin.mucang.cn/order-coach.kakamobi.cn',
  swallow: 'https://admin.mucang.cn/swallow.kakamobi.cn'
};

// !!!!--------------- 本地开发环境 appName 对应的地址
const localMap = {
  swallow: 'http://127.0.0.1:5501/index.html'
};

function getAppUrlMap(isProd, isLocal) {
  let result = testMap;

  if (isProd) {
    result = prodMap;
  }

  if (isLocal) {
    result = Object.assign({}, testMap, localMap);
  }

  result = Object.assign({}, result);

  Object.keys(result).forEach(key => {
    let val = result[key];

    if (!val.endsWith('.html') && !val.endsWith('/')) {
      val = val + '/';
    }

    const url = new URL(val);

    url.search = baseSearch;

    result[key] = url;
  });

  return result as any as Record<string, URL>;
}

export { getAppUrlMap };
