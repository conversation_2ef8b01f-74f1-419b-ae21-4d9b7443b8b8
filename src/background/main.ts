import PostMessage from '@simplex/post-message';

const postMessage = new PostMessage();

postMessage.post('base://listent.open.tab').on(config => {
  // 跳转到空页面，空页面里参数二次跳转
  const { name, id } = config.app;

  const nextApp = config.opts.app;

  const appName = nextApp.split('!')[0];

  const menuId = 'placeholder-' + appName;

  postMessage.post('navigation.to', menuId, {
    appName: appName,
    title: name,
    query: { ...config.params, _paasFramework: true, id, name },
    target: '_newTab'
  });
});
