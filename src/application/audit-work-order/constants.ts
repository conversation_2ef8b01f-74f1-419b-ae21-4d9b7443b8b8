export enum AuditStatusEnum {
  wait = 1,
  doing,
  success,
  fail
}

export const AUDIT_STATUS_OPTIONS = [
  { value: AuditStatusEnum.wait, label: '待审核' },
  { value: AuditStatusEnum.doing, label: '审核中' },
  { value: AuditStatusEnum.success, label: '已完成' },
  { value: AuditStatusEnum.fail, label: '审核失败' }
];

export enum WorkOrderTypeEnum {
  TRAINING = 'STF',
  REGIST_PLACE = 'SRS',
  // EXAMINATION = 'NEF',
  // LOGO = 'NMC',
  COURSE = 'SC',
  MEDIA = 'SM',
  PROCESS = 'SJX'
}

export enum MediaBizTypeEnum {
  // 驾校封面图片
  JX_COVER_IMAGE = 20,
  // 驾校封面视频
  JX_COVER_VIDEO = 30,
  // 驾校图片素材
  JX_IMAGE_MATERIAL = 40,
  // 驾校视频素材
  JX_VIDEO_MATERIAL = 50
}

export const MEDIA_BIZ_TYPE_OPTIONS = [
  { value: MediaBizTypeEnum.JX_COVER_IMAGE, label: '驾校封面图片' },
  { value: MediaBizTypeEnum.JX_COVER_VIDEO, label: '驾校封面视频' },
  { value: MediaBizTypeEnum.JX_IMAGE_MATERIAL, label: '驾校图片素材' },
  { value: MediaBizTypeEnum.JX_VIDEO_MATERIAL, label: '驾校视频素材' }
];

// 工单类型
export const WORK_ORDER_TYPES = [
  { value: WorkOrderTypeEnum.TRAINING, label: '驾校展示训练场审核' },
  { value: WorkOrderTypeEnum.REGIST_PLACE, label: '驾校展示报名点审核' },
  // { value: WorkOrderTypeEnum.EXAMINATION, label: '考场' },
  // { value: WorkOrderTypeEnum.LOGO, label: 'logo' },
  { value: WorkOrderTypeEnum.COURSE, label: '驾校展示班型审核' },
  { value: WorkOrderTypeEnum.MEDIA, label: '驾校展示媒资审核' },
  { value: WorkOrderTypeEnum.PROCESS, label: '驾校展示流程' }
];

export enum MediaLabelEnum {
  LOGO = 40
}
