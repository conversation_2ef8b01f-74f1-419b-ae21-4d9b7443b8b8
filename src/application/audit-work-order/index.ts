import { defineComponent, reactive, toRefs, ref } from 'vue';
import { ModelController } from 'admin-library';
import AuditDialogComp from './comps/audit-dialog/index.vue';
import { COLUMNS } from './config';
import { WORK_ORDER_TYPES } from './constants';
import { StateModel, AuditWorkOrderResponse } from './types';
import { GetAuditWorkOderListStore } from './store';
import { Store } from '@simplex/simple-store';

export default defineComponent({
  components: {
    AuditDialogComp
  },
  setup() {
    const state = reactive<StateModel>({
      workOrderType: null,
      orderId: null
    });

    // constants
    const constants = {
      COLUMNS,
      WORK_ORDER_TYPES
    };

    const controller = new ModelController({
      table: {
        store: GetAuditWorkOderListStore
      },
      search: {
        flowDefinitionIdList: {
          store: new Store(WORK_ORDER_TYPES)
        }
      }
    });

    const components = {
      auditDialogCompRef: ref<InstanceType<typeof AuditDialogComp>>()
    };

    // methods
    const methods = {
      openAuditDialog(record: AuditWorkOrderResponse) {
        components.auditDialogCompRef.value.open(record);
      },
      /**
       * 刷新表格数据
       */
      onRefresh() {
        controller.tableRequest();
      }
    };

    return {
      ...toRefs(state),
      ...constants,
      ...components,
      ...methods,
      controller
    };
  }
});
