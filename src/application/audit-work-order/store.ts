import { PaasListResponse } from 'admin-library';
import { Store } from '@simplex/simple-store';
import { AuditWorkOderListStore } from '@/store/qin';
import { AuditWorkOrderResponse } from './types';
import { AUDIT_STATUS_OPTIONS } from './constants';

// 审核工单列表
export const GetAuditWorkOderListStore = new AuditWorkOderListStore<PaasListResponse<AuditWorkOrderResponse>>({});

export const AuditStatusStore = new Store(AUDIT_STATUS_OPTIONS);
