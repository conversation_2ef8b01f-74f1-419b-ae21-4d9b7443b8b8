<template>
  <pm-dialog
    v-model:visible="visible"
    :title="title"
    centered
    destroy-on-close
    width="650px"
    :confirm-loading="loading"
    @confirm="onConfirm"
    @close="onCancel"
  >
    <template v-if="visible">
      <m-form :model="formData" :label-col="{ style: 'width: 100px' }" :wrapper-col="{ span: 20 }" autocomplete="off">
        <m-alert
          style="margin-bottom: 15px"
          message="勾选除“其他”以外的所有选项代表通过，未全勾选或勾选了“其他”代表驳回"
          type="warning"
          show-icon
        />

        <!-- 训练场和报名点 -->
        <div v-if="[WorkOrderTypeEnum.TRAINING, WorkOrderTypeEnum.REGIST_PLACE].includes(formData.type)">
          <div class="audit-work-content">
            <div>
              <h3>{{ title }}名称</h3>
              <div style="margin-bottom: 20px">{{ nameAuditForm.name || '暂无名称' }}</div>
              <h3>{{ title }}地址</h3>
              <div :class="{ address: locationAuditForm.address }" @click="openMapDialog(locationAuditForm)">
                {{ locationAuditForm.address || '暂无地址' }}
              </div>
            </div>
          </div>
          <div class="audit-work-content" v-for="(auditItem, index) in imageAuditForm" :key="index">
            <div class="content-left">
              <img :src="auditItem.image.previewUrl" alt="" @click="openImageDialog(auditItem.image.previewUrl)" />
              <div class="edit" @click="openTrainImageEditorDialog(index, auditItem.image.previewUrl)">编辑</div>
            </div>
            <div class="content-right">
              <m-form-item>
                <div v-for="(item, i) in auditItem?.checkList" :key="i">
                  <m-checkbox :disabled="!item.auditItem.needUserCheck" v-model:checked="item.marker.userMarker">
                    {{ item.auditItem.desc }}
                  </m-checkbox>
                  <m-input
                    v-if="isOtherReason(item.auditItem.code) && item.auditItem.needUserCheck && item.marker.userMarker"
                    v-model:value="formData.otherReason[index]"
                  />
                </div>
              </m-form-item>
            </div>
          </div>
        </div>

        <!-- 班型 -->
        <div v-if="formData.type === WorkOrderTypeEnum.COURSE">
          <div class="audit-work-content">
            <div class="content-left">
              <h3>班型名称</h3>
              <div>{{ courseNameAuditForm.courseName || '暂无名称' }}</div>
            </div>
            <div class="content-right">
              <m-form-item>
                <div v-for="(item, i) in courseNameAuditForm?.checkList" :key="i">
                  <m-checkbox :disabled="!item.auditItem.needUserCheck" v-model:checked="item.marker.userMarker">
                    {{ item.auditItem.desc }}
                  </m-checkbox>
                </div>
              </m-form-item>
            </div>
          </div>
          <h3 v-if="courseImageAuditForms.length">图片</h3>
          <div class="audit-work-content" v-for="(auditItem, index) in courseImageAuditForms" :key="index">
            <div class="content-left">
              <img :src="auditItem.image.previewUrl" alt="" @click="openImageDialog(auditItem.image.previewUrl)" />
              <div class="edit" @click="openCourseImageEditorDialog(index, auditItem.image.previewUrl)">编辑</div>
            </div>
            <div class="content-right">
              <m-form-item>
                <div v-for="(item, i) in auditItem?.checkList" :key="i">
                  <m-checkbox :disabled="!item.auditItem.needUserCheck" v-model:checked="item.marker.userMarker">
                    {{ item.auditItem.desc }}
                  </m-checkbox>
                  <m-input
                    v-if="isOtherReason(item.auditItem.code) && item.auditItem.needUserCheck && item.marker.userMarker"
                    v-model:value="formData.otherReason[index]"
                  />
                </div>
              </m-form-item>
            </div>
          </div>
          <h3 v-if="courseDetailImageAuditForms.length">详情</h3>
          <div class="audit-work-content" v-for="(auditItem, index) in courseDetailImageAuditForms" :key="index">
            <div class="content-left">
              <img :src="auditItem.image.previewUrl" alt="" @click="openImageDialog(auditItem.image.previewUrl)" />
              <div class="edit" @click="openCourseDetailImageEditorDialog(index, auditItem.image.previewUrl)">编辑</div>
            </div>
            <div class="content-right">
              <m-form-item>
                <div v-for="(item, i) in auditItem?.checkList" :key="i">
                  <m-checkbox :disabled="!item.auditItem.needUserCheck" v-model:checked="item.marker.userMarker">
                    {{ item.auditItem.desc }}
                  </m-checkbox>
                  <m-input
                    v-if="isOtherReason(item.auditItem.code) && item.auditItem.needUserCheck && item.marker.userMarker"
                    v-model:value="formData.detailOtherReason[index]"
                  />
                </div>
              </m-form-item>
            </div>
          </div>
        </div>

        <!-- 媒资 -->
        <div v-if="formData.type === WorkOrderTypeEnum.MEDIA">
          <h3 v-if="mediaLabel === MediaLabelEnum.LOGO">logo</h3>
          <h3 v-else>{{ MEDIA_BIZ_TYPE_OPTIONS.find(item => item.value === mediaBizType)?.label }}</h3>
          <div class="audit-work-content">
            <div class="content-left">
              <img
                v-if="showMaterialImage"
                :src="mediaUrl?.previewUrl"
                alt=""
                @click="openImageDialog(mediaUrl?.previewUrl)"
              />
              <video v-if="showVideo" :src="mediaUrl?.imageUrl" controls></video>
              <div v-if="showMaterialImage" class="edit" @click="openMediaImageEditorDialog(mediaUrl?.previewUrl)">
                编辑
              </div>
            </div>
            <div class="content-right">
              <m-form-item>
                <div v-for="(item, index) in checkList" :key="index">
                  <m-checkbox :disabled="!item.auditItem.needUserCheck" v-model:checked="item.marker.userMarker">
                    {{ item.auditItem.desc }}
                  </m-checkbox>
                  <m-input
                    v-if="isOtherReason(item.auditItem.code) && item.auditItem.needUserCheck && item.marker.userMarker"
                    v-model:value="formData.otherReason[0]"
                  />
                </div>
              </m-form-item>
            </div>
          </div>
        </div>

        <mosaic-dialog-comp ref="mosaicRef" />

        <a-map-comp :isChangeMapType="true" :ref="aMapStore?.aMapRef" />
      </m-form>
    </template>
  </pm-dialog>
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less" scoped></style>
