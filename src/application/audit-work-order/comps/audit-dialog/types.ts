import { WorkOrderTypeEnum } from '@/application/audit-work-order/constants';

export interface StateModel {
  visible: boolean;
  loading: boolean;
  formData: FormDataModel;
  locationAuditForm: Partial<LocationAuditFormModel>;
  nameAuditForm: Partial<NameAuditFormModel>;
  imageAuditForm: ImageAuditFormModel[];
  // 班型图片
  courseImageAuditForms: CourseImageAuditFormModel[];
  // 班型详情图片
  courseDetailImageAuditForms: CourseImageAuditFormModel[];
  // 班型名称
  courseNameAuditForm: CourseNameAuditFormModel;
  // 媒资
  checkList: CourseMarkerModel[];
  // 媒资url
  mediaUrl: {
    editedImageEncodedData: string;
    imageUrl: string;
    previewUrl?: string;
  };
  // 媒资类型
  mediaBizType: number;
  mediaLabel: number;
  detail: WorkOderViewResponse;
}

export interface FormDataModel {
  nodeInstanceId: number;
  type: WorkOrderTypeEnum;
  processInstanceId: number;
  otherReason: string[];
  detailOtherReason: string[];
}

export type WorkOderViewResponse = TrianViewResponseModel | CourseViewResponseModel | MediaViewResponseModel;

export interface TrianViewResponseModel {
  // 训练场地址
  locationAuditForm: LocationAuditFormModel;
  // 训练场名称
  nameAuditForm: NameAuditFormModel;
  // 训练场图片
  imageAuditForm: ImageAuditFormModel[];
  nodeInstanceId: number;
  processInstanceId: number;
}

export interface CourseViewResponseModel {
  // 班型名称
  courseNameAuditForm: CourseNameAuditFormModel;
  // 班型图片
  courseImageAuditForms: CourseImageAuditFormModel[];
  // 班型详情图片
  courseDetailImageAuditForms: CourseImageAuditFormModel[];
  nodeInstanceId: number;
  processInstanceId: number;
}

export interface MediaViewResponseModel {
  // 媒资url
  image: {
    editedImageEncodedData: string;
    imageUrl: string;
  };
  otherData: OtherDataModel;
  // 媒资类型
  mediaBizType: number;
  // 媒资标签list
  checkList: CourseMarkerModel[];
  mediaLabel: number;
  nodeInstanceId: number;
  processInstanceId: number;
}

export interface LocationAuditFormModel {
  address: string;
  latitude: number;
  longitude: number;
  amapPOIByJx: boolean;
  checkList: CourseMarkerModel[];
}

export interface NameAuditFormModel {
  name: string;
  checkList: CourseMarkerModel[];
}

export interface ImageAuditFormModel {
  checkList: CourseMarkerModel[];
  image: {
    editedImageEncodedData: string;
    imageUrl: string;
    previewUrl?: string;
  };
}

export interface CourseNameAuditFormModel {
  courseName: string;
  checkList: CourseMarkerModel[];
}

export interface CourseImageAuditFormModel {
  checkList: CourseMarkerModel[];
  image: {
    editedImageEncodedData: string;
    imageUrl: string;
    previewUrl?: string;
  };
}

export interface OtherDataModel {
  checkMediaCover: string;
  video: VideoModel;
}

export interface VideoModel {
  audio: boolean;
  duration: number;
  encryptPlayUrls: string[];
  format: string;
  playUrls: PlayUrlsModel[];
  thumbnails: ThumbnailsModel[];
  id: string;
  videoId: string;
}

export interface PlayUrlsModel {
  encrypt: boolean;
  fileSize: number;
  height: number;
  md5: string;
  playUrl: string;
  quality: string;
  url: string;
  version: string;
  width: number;
}

export interface ThumbnailsModel {
  height: number;
  url: string;
  width: number;
}

export interface CourseMarkerModel {
  marker: MarkerModel;
  auditItem: AuditItemModel;
}

export interface AuditItemModel {
  auditRemark: string;
  checkPassStrategy: string;
  code: string;
  desc: string;
  needUserCheck: boolean;
}

export interface MarkerModel {
  auditRemark?: string;
  robotMarker?: boolean;
  userMarker?: boolean;
}

export interface EmitsModel {
  (e: 'success'): void;
}
