import { computed, defineComponent, reactive, ref, toRefs } from 'vue';
import { Form } from 'ant-design-vue';
import { MESSAGE_TYPE, MUtils } from '@paas/paas-library';
import { useOpenPreviewImgStore } from '@/pinia/open-preview-img';
import { useAMap } from '@/components/a-map/utils';
import AMapComp from '@/components/a-map/index.vue';
import MosaicDialogComp from '@/components/mosaic-dialog/index.vue';
import {
  MEDIA_BIZ_TYPE_OPTIONS,
  MediaBizTypeEnum,
  MediaLabelEnum,
  WORK_ORDER_TYPES,
  WorkOrderTypeEnum
} from '@/application/audit-work-order/constants';
import { AuditWorkOrderResponse } from '@/application/audit-work-order/types';
import { GetAuditWorkOderViewStore, PostProcessAuditWorkOderStore } from './store';
import {
  EmitsModel,
  LocationAuditFormModel,
  CourseMarkerModel,
  TrianViewResponseModel,
  CourseViewResponseModel,
  MediaViewResponseModel,
  StateModel
} from './types';

export default defineComponent({
  components: {
    AMapComp,
    MosaicDialogComp
  },
  setup(props, { emit }: { emit: EmitsModel }) {
    const useForm = Form.useForm;
    const openPreviewImgStore = useOpenPreviewImgStore();
    const aMapStore = useAMap();

    const state = reactive<StateModel>({
      visible: false,
      loading: false,
      // 训练场和报名点地址
      locationAuditForm: {
        address: null,
        latitude: null,
        longitude: null,
        amapPOIByJx: false,
        checkList: []
      },
      // 训练场和报名点名称
      nameAuditForm: {
        name: null,
        checkList: []
      },
      // 训练场和报名点图片
      imageAuditForm: [],
      // 训练场和报名点表单数据
      formData: {
        nodeInstanceId: null,
        type: null,
        processInstanceId: null,
        // 其他原因
        otherReason: [],
        detailOtherReason: []
      },
      // 班型图片
      courseImageAuditForms: [],
      // 班型详情图片
      courseDetailImageAuditForms: [],
      // 班型名称
      courseNameAuditForm: {
        courseName: null,
        checkList: []
      },
      // 媒资
      checkList: [],
      // 媒资url
      mediaUrl: null,
      // 媒资类型
      mediaBizType: null,
      mediaLabel: null,
      detail: null
    });

    const constants = {
      WorkOrderTypeEnum,
      MEDIA_BIZ_TYPE_OPTIONS,
      MediaLabelEnum,
      MediaBizTypeEnum
    };

    const computeds = {
      title: computed(() => {
        return WORK_ORDER_TYPES.find(item => item.value === state.formData.type)?.label || '';
      }),
      showMaterialImage: computed(() => {
        return (
          [MediaBizTypeEnum.JX_COVER_IMAGE, MediaBizTypeEnum.JX_IMAGE_MATERIAL].includes(state.mediaBizType) ||
          state.mediaLabel === MediaLabelEnum.LOGO
        );
      }),
      showVideo: computed(() => {
        return (
          [MediaBizTypeEnum.JX_COVER_VIDEO, MediaBizTypeEnum.JX_VIDEO_MATERIAL].includes(state.mediaBizType) &&
          state.mediaUrl?.imageUrl
        );
      })
    };

    const components = {
      mosaicRef: ref<InstanceType<typeof MosaicDialogComp>>(null)
    };

    const methods = {
      open(record: AuditWorkOrderResponse) {
        state.visible = true;
        state.formData.nodeInstanceId = record.nodeInstanceId;
        state.formData.type = record.flowDefinitionId;
        methods.getAuditWorkOrderDetails();
      },
      // 获取工单详情
      getAuditWorkOrderDetails() {
        GetAuditWorkOderViewStore.request({
          nodeInstanceId: state.formData.nodeInstanceId
        })
          .getData()
          .then(res => {
            state.detail = res;
            state.formData.processInstanceId = res.processInstanceId;
            if ([WorkOrderTypeEnum.TRAINING, WorkOrderTypeEnum.REGIST_PLACE].includes(state.formData.type)) {
              // 训练场和报名点工单
              const trainRes = res as TrianViewResponseModel;
              state.imageAuditForm = trainRes.imageAuditForm || [];
              state.imageAuditForm.forEach(item => {
                // 增加预览图，原图不能修改传给接口
                item.image.previewUrl = item.image.imageUrl;
              });
              state.locationAuditForm = trainRes.locationAuditForm || {};
              state.nameAuditForm = trainRes.nameAuditForm || {};
            } else if (state.formData.type === WorkOrderTypeEnum.COURSE) {
              // 班型工单
              const courseRes = res as CourseViewResponseModel;
              state.courseImageAuditForms = courseRes.courseImageAuditForms || [];
              state.courseImageAuditForms.forEach(item => {
                item.image.previewUrl = item.image.imageUrl;
              });
              state.courseDetailImageAuditForms = courseRes.courseDetailImageAuditForms || [];
              state.courseDetailImageAuditForms.forEach(item => {
                item.image.previewUrl = item.image.imageUrl;
              });
              state.courseNameAuditForm = courseRes.courseNameAuditForm;
            } else if (state.formData.type === WorkOrderTypeEnum.MEDIA) {
              const mediaRes = res as MediaViewResponseModel;
              // 媒资工单
              state.checkList = mediaRes.checkList;
              state.mediaUrl = mediaRes.image;
              state.mediaUrl.previewUrl = mediaRes.image.imageUrl;
              state.mediaBizType = mediaRes.mediaBizType;
              state.mediaLabel = mediaRes.mediaLabel;
            }
            console.log(55555, res);
          });
      },
      onConfirm() {
        validate().then(() => {
          if (!state.formData.processInstanceId) {
            MUtils.toast('未获取到审核详情', MESSAGE_TYPE.error);
            return;
          }

          const flowFormBO = methods.setFormData();

          state.loading = true;

          PostProcessAuditWorkOderStore.request({
            processInstanceId: state.formData.processInstanceId,
            nodeInstanceId: state.formData.nodeInstanceId,
            flowFormBOJSON: JSON.stringify(flowFormBO)
          })
            .getData()
            .then(() => {
              emit('success');
              MUtils.toast('编辑成功', MESSAGE_TYPE.success);
              methods.onCancel();
            })
            .finally(() => {
              state.loading = false;
            });
        });
      },
      // 设置提交数据
      setFormData() {
        console.log(state.formData.otherReason, state.imageAuditForm, state.detail);
        if ([WorkOrderTypeEnum.TRAINING, WorkOrderTypeEnum.REGIST_PLACE].includes(state.formData.type)) {
          state.imageAuditForm.forEach((item, index) => {
            methods.setCheckListData(item.checkList, index);
          });
          return {
            ...state.detail,
            imageAuditForm: state.imageAuditForm
          };
        } else if (state.formData.type === WorkOrderTypeEnum.COURSE) {
          methods.setCheckListData(state.courseNameAuditForm.checkList);
          state.courseImageAuditForms.forEach((item, index) => {
            methods.setCheckListData(item.checkList, index);
          });
          state.courseDetailImageAuditForms.forEach((item, index) => {
            methods.setCheckListData(item.checkList, index, state.formData.detailOtherReason);
          });
          return {
            ...state.detail,
            courseImageAuditForms: state.courseImageAuditForms,
            courseDetailImageAuditForms: state.courseDetailImageAuditForms,
            courseNameAuditForm: state.courseNameAuditForm
          };
        } else if (state.formData.type === WorkOrderTypeEnum.MEDIA) {
          methods.setCheckListData(state.checkList, 0);
          return {
            ...state.detail,
            checkList: state.checkList,
            image: state.mediaUrl
          };
        }
      },
      // 设置图片审核数据
      setCheckListData(list: CourseMarkerModel[], currentIndex?: number, otherReasonList?: string[]) {
        list.forEach(item => {
          if (item.auditItem.needUserCheck) {
            const otherReason = otherReasonList || state.formData.otherReason;
            const isOther = methods.isOtherReason(item.auditItem.code);
            // 需要用户审核的数据，默认为false
            const userMarker = item.marker.userMarker || false;

            item.marker.auditRemark = isOther
              ? userMarker && currentIndex >= 0
                ? otherReason[currentIndex]
                : item.auditItem.auditRemark
              : item.marker.auditRemark;
            item.marker.userMarker = userMarker;
          }
        });
      },
      isOtherReason(code: string) {
        return ['imageOther', 'videoOther'].includes(code);
      },
      openMapDialog(locationAuditForm: Partial<LocationAuditFormModel>) {
        if (!locationAuditForm.longitude || !locationAuditForm.latitude) {
          return;
        }
        aMapStore.aMapRef.value.open({
          name: locationAuditForm.address,
          longitude: locationAuditForm.longitude,
          latitude: locationAuditForm.latitude
        });
      },
      // 编辑训练场和报名点图片
      async openTrainImageEditorDialog(index: number, imageUrl: string) {
        const mosaic = await components.mosaicRef.value.open(imageUrl);
        if (mosaic?.encodedData) {
          state.imageAuditForm[index].image.previewUrl = mosaic.previewUrl;
          state.imageAuditForm[index].image.editedImageEncodedData = mosaic.encodedData;
        }
      },
      // 编辑班型图片
      async openCourseImageEditorDialog(index: number, imageUrl: string) {
        const mosaic = await components.mosaicRef.value.open(imageUrl);
        if (mosaic?.encodedData) {
          state.courseImageAuditForms[index].image.previewUrl = mosaic.previewUrl;
          state.courseImageAuditForms[index].image.editedImageEncodedData = mosaic.encodedData;
        }
      },
      // 编辑班型详情图片
      async openCourseDetailImageEditorDialog(index: number, imageUrl: string) {
        const mosaic = await components.mosaicRef.value.open(imageUrl);
        if (mosaic?.encodedData) {
          state.courseDetailImageAuditForms[index].image.previewUrl = mosaic.previewUrl;
          state.courseDetailImageAuditForms[index].image.editedImageEncodedData = mosaic.encodedData;
        }
      },
      // 编辑媒资图片
      async openMediaImageEditorDialog(imageUrl: string) {
        const mosaic = await components.mosaicRef.value.open(imageUrl);
        if (mosaic?.encodedData) {
          state.mediaUrl.previewUrl = mosaic.previewUrl;
          state.mediaUrl.editedImageEncodedData = mosaic.encodedData;
        }
      },
      /**
       * 打开图片预览弹窗
       * @param imageList 图片列表
       */
      openImageDialog(imageUrl: string) {
        openPreviewImgStore.open({
          url: [imageUrl],
          type: 0
        });
      },
      onCancel() {
        methods.clearAllFormData();
        state.visible = false;
      },
      clearAllFormData() {
        resetFields();
        state.imageAuditForm = [];
        state.nameAuditForm = {
          name: null
        };
        state.locationAuditForm = {
          address: null,
          latitude: null,
          longitude: null,
          amapPOIByJx: false
        };
        state.courseImageAuditForms = [];
        state.courseDetailImageAuditForms = [];
        state.courseNameAuditForm = {
          courseName: null,
          checkList: []
        };
        state.mediaUrl = null;
        state.detail = null;
      }
    };

    const rules = reactive({});

    //表单校验
    const { validate, resetFields, validateInfos } = useForm(state.formData, rules);

    return {
      aMapStore,
      validateInfos,
      ...toRefs(state),
      ...components,
      ...constants,
      ...computeds,
      ...methods
    };
  }
});
