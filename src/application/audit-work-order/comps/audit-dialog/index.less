.audit-work-content {
  display: flex;
  padding: 20px 0;
  margin-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
  &:first-child {
    padding-top: 0;
  }
  &:last-child {
    border-bottom: none;
  }
  .content-left {
    position: relative;
    flex: none;
    width: 300px;
    img {
      width: 100%;
      cursor: pointer;
    }
    video {
      width: 100%;
      cursor: pointer;
    }
    .address {
      color: var(--ant-primary-color);
      cursor: pointer;
    }
    .edit {
      position: absolute;
      top: 0;
      right: 0;
      padding: 5px 10px;
      background: #333333;
      color: #ffffff;
      cursor: pointer;
    }
  }
  .content-right {
    flex: 1;
    margin-left: 30px;
  }
}
