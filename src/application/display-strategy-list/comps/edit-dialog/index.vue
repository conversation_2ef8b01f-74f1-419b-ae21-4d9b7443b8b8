<template>
  <pm-dialog
    v-model:visible="visible"
    :title="isAdd ? '新增展示策略' : '编辑展示策略'"
    :width="600"
    :confirm-loading="loading"
    @confirm="handleSubmit"
    @close="handleCancel"
  >
    <m-form
      class="display-strategy-form"
      ref="formRef"
      :model="formData"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
    >
      <m-form-item label="城市" name="cityCode" :rules="[{ required: true, message: '请选择城市', trigger: 'change' }]">
        <m-cascader
          style="width: 300px"
          v-model:value="formData.cityCode"
          :disabled="!isAdd"
          :options="cityDataStore.data"
          :allowClear="true"
          change-on-select
          placeholder="城市"
        />
      </m-form-item>

      <m-form-item label="授权主体" name="authorizedShow">
        <m-radio-group :disabled="!isAdd && formData.authorizedShow === null" v-model:value="formData.authorizedShow">
          <m-radio v-for="option in AUTHORIZED_ENTITY_OPTIONS" :key="option.label" :value="option.value">
            {{ option.label }}
          </m-radio>
        </m-radio-group>
      </m-form-item>

      <m-form-item label="授权个人" name="registrationShow">
        <m-radio-group
          :disabled="!isAdd && formData.registrationShow === null"
          v-model:value="formData.registrationShow"
        >
          <m-radio v-for="option in AUTHORIZED_PERSONAL_OPTIONS" :key="option.label" :value="option.value">
            {{ option.label }}
          </m-radio>
        </m-radio-group>
      </m-form-item>

      <m-form-item label="不开启自动提示" name="strictEnable">
        <m-radio-group :disabled="!isAdd && formData.strictEnable === null" v-model:value="formData.strictEnable">
          <m-radio v-for="option in DISABLE_AUTO_PROMPT_OPTIONS" :key="option.label" :value="option.value">
            {{ option.label }}
          </m-radio>
        </m-radio-group>
      </m-form-item>
    </m-form>
  </pm-dialog>
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less" scoped></style>
