import { defineComponent, ref, reactive, toRefs } from 'vue';
import { MESSAGE_TYPE, MUtils } from '@paas/paas-library';
import { useCityDataStore } from '@/pinia/city-data';
import { GetUpdateDisplayStrategyStore } from '../../store';
import { DisplayStrategyListResponse } from '../../types';
import { AUTHORIZED_ENTITY_OPTIONS, AUTHORIZED_PERSONAL_OPTIONS, DISABLE_AUTO_PROMPT_OPTIONS } from '../../constants';
import { StateModel, EmitsModel } from './types';

export default defineComponent({
  emits: ['success'],
  setup(props, { emit }: { emit: EmitsModel }) {
    const cityDataStore = useCityDataStore();

    // 状态
    const state = reactive<StateModel>({
      visible: false,
      loading: false,
      isAdd: false,
      formData: {
        cityCode: [],
        authorizedShow: null,
        registrationShow: null,
        strictEnable: null
      }
    });

    // 常量
    const constants = {
      AUTHORIZED_ENTITY_OPTIONS,
      AUTHORIZED_PERSONAL_OPTIONS,
      DISABLE_AUTO_PROMPT_OPTIONS
    };

    // 组件引用
    const components = {
      formRef: ref()
    };

    // 方法
    const methods = {
      /**
       * 打开对话框
       */
      open(data?: DisplayStrategyListResponse) {
        state.visible = true;
        state.isAdd = !data;
        if (state.isAdd) {
          state.formData = {
            cityCode: [],
            authorizedShow: null,
            registrationShow: null,
            strictEnable: null
          };
        } else {
          state.formData.cityCode = [];
          methods.getNodeRoute(cityDataStore.data, data.cityCode);
          const { authorizedShow, registrationShow, strictEnable } = data;
          Object.assign(state.formData, {
            cityCode: state.formData.cityCode.reverse(),
            authorizedShow,
            registrationShow,
            strictEnable
          });
        }
      },

      /**
       * 关闭对话框
       */
      close() {
        state.visible = false;
        components.formRef.value?.resetFields();
      },

      // 取全路径
      getNodeRoute(tree, targetId) {
        for (let index = 0; index < tree.length; index++) {
          if (tree[index].children) {
            const endRecursiveLoop = methods.getNodeRoute(tree[index].children, targetId);
            if (endRecursiveLoop) {
              state.formData.cityCode.push(tree[index].value);
              return true;
            }
          }
          if (tree[index].value === targetId) {
            state.formData.cityCode.push(tree[index].value);
            return true;
          }
        }
      },

      /**
       * 提交表单
       */
      async handleSubmit() {
        components.formRef.value?.validate().then(() => {
          state.loading = true;

          const formData = {
            cityCode: state.formData.cityCode[state.formData.cityCode.length - 1],
            authorizedShow: state.formData.authorizedShow,
            registrationShow: state.formData.registrationShow,
            strictEnable: state.formData.strictEnable
          };

          // 调用API更新数据
          GetUpdateDisplayStrategyStore.request(formData)
            .getData()
            .then(() => {
              MUtils.toast(state.isAdd ? '添加成功' : '更新成功', MESSAGE_TYPE.success);
              emit('success');
              methods.close();
            })
            .finally(() => {
              state.loading = false;
            });
        });
      },

      /**
       * 取消操作
       */
      handleCancel() {
        methods.close();
      }
    };

    return {
      cityDataStore,
      ...toRefs(state),
      ...constants,
      ...components,
      ...methods
    };
  }
});
