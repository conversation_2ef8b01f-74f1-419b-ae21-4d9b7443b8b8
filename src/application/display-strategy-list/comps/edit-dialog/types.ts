/**
 * 编辑对话框状态模型
 */
export interface StateModel {
  /** 是否显示对话框 */
  visible: boolean;
  /** 加载状态 */
  loading: boolean;
  isAdd: boolean;
  /** 表单数据 */
  formData: FormDataModel;
}

/**
 * 表单数据模型
 */
export interface FormDataModel {
  cityCode: string[];
  /** 授权主体状态 0-不展示 1-展示 */
  authorizedShow: boolean;
  /** 授权个人状态 0-不展示 1-展示 */
  registrationShow: boolean;
  /** 不开启自动提示 0-否 1-是 */
  strictEnable: boolean;
}

/**
 * 事件模型
 */
export interface EmitsModel {
  (e: 'success'): void;
}
