import { defineComponent, ref, reactive, toRefs } from 'vue';
import { ModelController } from 'admin-library';
import EditDialog from './comps/edit-dialog/index.vue';
import { COLUMNS } from './config';
import { GetDisplayStrategyListStore } from './store';
import { StateModel, DisplayStrategyListResponse } from './types';
import { useCityDataStore } from '@/pinia/city-data';
export default defineComponent({
  components: {
    EditDialog
  },
  setup() {
    const cityDataStore = useCityDataStore();
    // 状态
    const state = reactive<StateModel>({
      cityCode: []
    });

    // 常量
    const constants = {
      COLUMNS,
      cityDataStore
    };

    // 控制器
    const controller = new ModelController({
      table: {
        store: GetDisplayStrategyListStore
      }
    });

    controller.table.onRequest.use(params => {
      if (state.cityCode?.length) {
        params.cityCode = state.cityCode[state.cityCode.length - 1];
      }
      return params;
    });

    // 组件引用
    const components = {
      editDialogRef: ref<InstanceType<typeof EditDialog>>()
    };

    // 方法
    const methods = {
      handleAdd() {
        components.editDialogRef.value?.open();
      },
      /**
       * 处理编辑操作
       */
      handleEdit(record: DisplayStrategyListResponse) {
        components.editDialogRef.value?.open(record);
      },

      /**
       * 编辑成功回调
       */
      onEditSuccess() {
        // 刷新表格数据
        controller.tableRequest();
      },

      /**
       * 刷新表格数据
       */
      onRefresh() {
        controller.tableRequest();
      }
    };

    return {
      ...toRefs(state),
      ...constants,
      ...components,
      ...methods,
      controller
    };
  }
});
