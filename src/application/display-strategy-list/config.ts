import { TableColumn } from 'admin-library';

export const COLUMNS: TableColumn[] = [
  {
    title: '城市',
    dataIndex: 'cityName',
    width: 150,
    fixed: 'left'
  },
  {
    title: '授权主体',
    dataIndex: 'authorizedShow',
    width: 120,
    render: (value: boolean) => {
      return value === true ? '展示' : value === false ? '不展示' : '';
    }
  },
  {
    title: '授权个人',
    dataIndex: 'registrationShow',
    width: 120,
    render: (value: boolean) => {
      return value === true ? '展示' : value === false ? '不展示' : '';
    }
  },
  {
    title: '不开启自动提示',
    dataIndex: 'strictEnable',
    width: 150,
    render: (value: boolean) => {
      return value === true ? '是' : value === false ? '否' : '';
    }
  }
];
