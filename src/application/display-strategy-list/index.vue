<template>
  <pm-effi :controller="controller">
    <pm-search class="padding-between">
      <pm-search-single :span="4" data-index="cityCode">
        <template #custom>
          <m-cascader
            v-model:value="cityCode"
            :options="cityDataStore.data"
            :allowClear="true"
            change-on-select
            placeholder="城市"
          />
        </template>
      </pm-search-single>
    </pm-search>

    <pm-table
      :columns="COLUMNS"
      :useCustomColumn="true"
      :sortNum="false"
      :operations-width="100"
      :operations-fixed="true"
      :pagination="false"
      :antdProps="{
        pagination: false,
        bordered: true,
        size: 'small'
      }"
    >
      <template #beforeButtons>
        <m-button type="primary" @click="handleAdd">新增</m-button>
      </template>
      <!-- 操作 -->
      <template #operations="{ record }">
        <m-button type="link" @click="handleEdit(record)">编辑</m-button>
      </template>
    </pm-table>
  </pm-effi>

  <!-- 编辑对话框 -->
  <EditDialog ref="editDialogRef" @success="onEditSuccess" />
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less" scoped></style>
