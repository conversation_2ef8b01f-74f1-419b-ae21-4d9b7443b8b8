import { defineComponent, reactive, ref, toRefs } from 'vue';
import { ModelController } from 'admin-library';
import AddDialog from './comps/add-dialog/index.vue';
import { MESSAGE_TYPE, MUtils } from '@paas/paas-library';
import { COLUMNS } from './config';
import { GetHideChangeLogListStore, GetUpdateDisableHideChangeLogStore } from './store';
import { BlacklistWhitelistResponse } from './types';

export default defineComponent({
  components: {
    AddDialog
  },
  setup() {
    // 常量
    const constants = {
      COLUMNS
    };

    const state = reactive({
      // 可以添加一些状态管理
    });

    // 控制器
    const controller = new ModelController({
      table: {
        store: GetHideChangeLogListStore
      }
    });

    // 组件引用
    const components = {
      addDialogRef: ref<InstanceType<typeof AddDialog>>()
    };

    // 方法
    const methods = {
      /**
       * 打开新增黑白名单弹框
       */
      openAddDialog() {
        components.addDialogRef.value?.open();
      },

      /**
       * 切换状态
       */
      toggleStatus(record: any) {
        // 更新黑白名单状态
        GetUpdateDisableHideChangeLogStore.request({
          disable: !record.disable,
          jiaxiaoId: record.jiaxiaoId
        })
          .getData()
          .then(() => {
            MUtils.toast('修改成功', MESSAGE_TYPE.success);
            controller.tableRequest();
          });
      },
      /**
       * 刷新列表
       */
      onRefresh() {
        controller.tableRequest();
      }
    };

    return {
      ...toRefs(state),
      ...constants,
      ...components,
      ...methods,
      controller
    };
  }
});
