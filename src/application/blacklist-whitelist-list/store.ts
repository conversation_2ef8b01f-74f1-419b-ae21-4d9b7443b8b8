import { PaasListResponse } from 'admin-library';
import { BlacklistWhitelistResponse } from './types';
import { HideChangeLogListStore, UpdateDisableHideChangeLogStore } from '@/store/mclaren';

// 黑白名单列表
export const GetHideChangeLogListStore = new HideChangeLogListStore<PaasListResponse<BlacklistWhitelistResponse>>({});

// 更新黑白名单失效状态
export const GetUpdateDisableHideChangeLogStore = new UpdateDisableHideChangeLogStore<{ value: boolean }>({});
