<template>
  <div class="blacklist-whitelist-list">
    <pm-effi :controller="controller">
      <pm-search class="padding-between">
        <pm-search-single
          :antdProps="{
            placeholder: '驾校ID',
            type: 'text'
          }"
          data-index="jiaxiaoId"
          xtype="INPUT"
        />
      </pm-search>

      <pm-table
        :columns="COLUMNS"
        :useCustomColumn="true"
        :sortNum="false"
        :operations-width="60"
        :operations-fixed="true"
        :antdProps="{
          pagination: false,
          bordered: true,
          size: 'small'
        }"
      >
        <template #beforeButtons>
          <m-button type="primary" @click="openAddDialog">新增</m-button>
        </template>

        <!-- 操作项 -->
        <template #operations="{ record }">
          <m-button type="link" :class="record.disable ? 'btn-primary' : 'btn-danger'" @click="toggleStatus(record)">
            {{ record.disable ? '生效' : '失效' }}
          </m-button>
        </template>
      </pm-table>
    </pm-effi>

    <!-- 新增黑白名单弹框 -->
    <add-dialog ref="addDialogRef" @success="onRefresh" />
  </div>
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less" scoped></style>
