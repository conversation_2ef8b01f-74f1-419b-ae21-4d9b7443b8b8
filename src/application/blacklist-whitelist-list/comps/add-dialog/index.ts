import { defineComponent, reactive, ref, toRefs } from 'vue';
import { MESSAGE_TYPE, MUtils } from '@paas/paas-library';
import { GetAddHideChangeLogStore } from './store';
import { LIST_TYPE_LABEL, LIST_STATUS_LABEL, FORM_RULES } from './constants';
import { Form } from 'ant-design-vue';

export default defineComponent({
  setup(props, { emit }) {
    const useForm = Form.useForm;
    const constants = {
      LIST_TYPE_LABEL,
      LIST_STATUS_LABEL,
      FORM_RULES
    };

    const state = reactive({
      formData: {
        jiaxiaoId: null,
        type: 'WHITE',
        disable: false,
        remark: null
      },
      rules: FORM_RULES,
      visible: false
    });

    // 组件引用
    const components = {};

    // 方法
    const methods = {
      /**
       * 打开新增黑白名单弹框
       */
      open() {
        state.visible = true;
        state.formData.jiaxiaoId = null;
        state.formData.type = 'WHITE';
        state.formData.disable = false;
        state.formData.remark = null;
      },

      /**
       * 新增黑白名单
       */
      handleSubmit() {
        validate().then(() => {
          // 更新黑白名单状态
          GetAddHideChangeLogStore.request({
            jiaxiaoIds: state.formData.jiaxiaoId,
            disable: state.formData.disable,
            type: state.formData.type,
            remark: state.formData.remark
          })
            .getData()
            .then(() => {
              MUtils.toast('添加成功', MESSAGE_TYPE.success);
              state.visible = false;
              emit('success');
            });
        });
      },
      /**
       * 关闭新增黑白名单弹框
       */
      handleCancel() {
        state.visible = false;
        resetFields();
      }
    };

    // 表单校验
    const { validate, resetFields, validateInfos, clearValidate } = useForm(state.formData, state.rules);
    return {
      ...toRefs(state),
      ...constants,
      ...components,
      ...methods,
      validateInfos
    };
  }
});
