/**
 * 新增黑白名单弹框类型定义
 */

/** 新增黑白名单请求参数 */
export interface AddBlacklistWhitelistRequest {
  /** 驾校ID字符串 */
  jiaxiaoId: string;
  /** 驾校ID数组 */
  jiaxiaoIds: number[];
  /** 驾校名称 */
  jiaxiaoName: string;
  /** 名单类型：1-黑名单，2-白名单 */
  listType: number;
  /** 列入理由 */
  reason: string;
  /** 是否失效 */
  disable: boolean;
}

/** 表单验证规则类型 */
export interface FormRule {
  required?: boolean;
  message?: string;
  trigger?: string | string[];
  validator?: (rule: any, value: any) => Promise<void> | void;
}

/** 表单验证规则集合 */
export interface FormRules {
  jiaxiaoId: FormRule[];
  jiaxiaoName: FormRule[];
  listType: FormRule[];
  reason: FormRule[];
}

/** 弹框配置类型 */
export interface DialogConfig {
  width: string;
  title: string;
  labelColSpan: number;
  wrapperColSpan: number;
  textareaRows: number;
}
