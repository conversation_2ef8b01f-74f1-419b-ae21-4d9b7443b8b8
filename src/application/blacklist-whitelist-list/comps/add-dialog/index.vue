<template>
  <pm-dialog v-model:visible="visible" title="新增黑白名单" width="600px" @close="handleCancel">
    <m-form ref="formRef" :model="formData" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
      <m-form-item label="驾校ID" v-bind="validateInfos.jiaxiaoId">
        <m-input v-model:value="formData.jiaxiaoId" placeholder="请输入驾校ID，多个用逗号分隔" />
      </m-form-item>

      <m-form-item label="名单类型" v-bind="validateInfos.type">
        <m-select v-model:value="formData.type" placeholder="请选择名单类型">
          <m-select-option v-for="item in LIST_TYPE_LABEL" :key="item.value" :value="item.value">
            {{ item.label }}
          </m-select-option>
        </m-select>
      </m-form-item>
      <m-form-item label="名单是否失效" v-bind="validateInfos.disable">
        <m-radio-group v-model:value="formData.disable">
          <m-radio v-for="item in LIST_STATUS_LABEL" :key="item.value" :value="item.value">
            {{ item.label }}
          </m-radio>
        </m-radio-group>
      </m-form-item>

      <m-form-item label="名单列入理由" v-bind="validateInfos.remark">
        <m-textarea v-model:value="formData.remark" placeholder="请输入列入名单的理由" :maxlength="100" :rows="2" />
      </m-form-item>
    </m-form>
    <template #footer>
      <m-button key="back" @click="handleCancel">取消</m-button>
      <m-button key="submit" type="primary" @click="handleSubmit">保存</m-button>
    </template>
  </pm-dialog>
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="less" scoped>
.add-dialog {
  .ant-form-item {
    margin-bottom: 16px;
  }
}
</style>
