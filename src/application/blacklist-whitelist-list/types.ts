/**
 * 黑白名单列表响应数据
 */
export interface BlacklistWhitelistResponse {
  /** 主键ID */
  id: number;
  /** 驾校ID */
  jiaxiaoId: number;
  /** 驾校名称 */
  jiaxiaoName: string;
  /** 名单类型 1-黑名单 2-白名单 */
  listType: number;
  /** 列入名单的理由 */
  reason: string;
  /** 创建人 */
  creator: string;
  /** 创建时间 */
  createTime: string;
  /** 操作人 */
  operator: string;
  /** 操作时间 */
  operationTime: string;
  /** 状态 1-生效 0-失效 */
  status: number;
}

/**
 * 新增黑白名单请求数据
 */
export interface AddBlacklistWhitelistRequest {
  /** 驾校ID字符串，多个用逗号分隔 */
  jiaxiaoId: string;
  /** 驾校ID数组，用于后端处理 */
  jiaxiaoIds: number[];
  /** 驾校名称 */
  jiaxiaoName: string;
  /** 名单类型 1-黑名单 2-白名单 */
  listType: number;
  /** 列入名单的理由 */
  reason: string;
}

/**
 * 名单类型选项
 */
export interface ListTypeOption {
  label: string;
  value: number;
}

/**
 * 状态选项
 */
export interface StatusOption {
  label: string;
  value: number;
}
