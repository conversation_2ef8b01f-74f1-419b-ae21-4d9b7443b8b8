import { TableColumn, TableDateFormat } from 'admin-library';
import { LIST_TYPE_OPTIONS } from './constants';
import { findText } from '@/utils/utils';

export const COLUMNS: TableColumn[] = [
  {
    title: '驾校ID',
    dataIndex: 'jiaxiaoId',
    width: 100,
    fixed: 'left'
  },
  {
    title: '驾校名称',
    dataIndex: 'jiaxiaoName',
    width: 200,
    fixed: 'left',
    oneline: true
  },
  {
    title: '名单类型',
    dataIndex: 'type',
    width: 100,
    render: (text: string) => {
      return findText(LIST_TYPE_OPTIONS, text);
    }
  },
  {
    title: '列入名单的理由',
    dataIndex: 'remark',
    width: 160,
    oneline: true
  },
  {
    title: '创建人',
    dataIndex: 'createUserName',
    width: 100
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 160,
    dateFormat: TableDateFormat.SECONDS
  },
  {
    title: '操作人',
    dataIndex: 'updateUserName',
    width: 100
  },
  {
    title: '操作时间',
    dataIndex: 'updateTime',
    width: 160,
    dateFormat: TableDateFormat.SECONDS
  }
];
