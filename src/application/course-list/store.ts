import { PaasListResponse } from 'admin-library';
import { Store } from '@simplex/simple-store';
import { CourseShowListStore } from '@/store/mclaren';
import { CodeListStore } from '@/store/qin';
import { CourseListResponse, OptionItem } from './types';
import { DISPLAY_STATUS_OPTIONS } from './constants';

// 班型列表
export const GetCourseListStore = new CourseShowListStore<PaasListResponse<CourseListResponse>>({});

// 驾照类型列表
export const GetDriveLicenseStore = new CodeListStore<PaasListResponse<OptionItem>>({
  before: options => {
    options.params = {
      ...options.params,
      ...{
        enumClassSimpleName: 'DriveLicenseTypeEnum'
      }
    };
    return options;
  },
  dataFilter: (data: PaasListResponse<OptionItem>) => {
    return data.itemList.filter(item => item.code > 0);
  }
});

// 展示状态下拉选项
export const DisplayStatusStore = new Store(DISPLAY_STATUS_OPTIONS);
