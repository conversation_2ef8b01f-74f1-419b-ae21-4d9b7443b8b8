export interface StateModel {
  driveLicenseList: OptionItem[];
  driveLicenseTypes: string | null;
}

export interface CourseImageResponse {
  /**  */
  encodedData: string;
  /**  */
  url: string;
}
export interface CourseDetailImagesResponse {
  /**  */
  encodedData: string;
  /**  */
  url: string;
}
export interface CourseFeeDetailsResponse {
  /**  */
  categoryName: string;
  /**  */
  feeMoney: number;
}
export interface CourseResponse {
  /** 班型id */
  id: number;
  /** 来源类型,可用值:UNKNOWN,PAAS */
  sourceType: string;
  /** 来源id */
  sourceId: number;
  /** 驾校id */
  jiaxiaoId: number;
  /** 排序标识 */
  sortNum: number;
  /** 排序时间 */
  sortTime: number;
  /** 展示状态,可用值:HIDE,SHOW */
  showStatus: string;
  /** 班型名称 */
  name: string;
  /** 班型类型,可用值:GENERAL,WEEKEND,NIGHT,VIP,NON_LOCAL,FAST,WEEKDAY,OLD,LADY,SALE,VACATION,ALLWEEKDAY,ROCKET,PRIVATE,FIXEDPRICE,OTHER */
  courseClass: string;
  /** 驾照类型,可用值:UNKNOWN,A1,A2,A3,B1,B2,C1,C2,C3,C4,C5,C6,D,E,F,M,N,P,UA_M,UA_F,UA_H,UA_V */
  licenseType: string;
  /** 接送方式,可用值:COACH_PICK_UP,BY_BUS,GO_SELF */
  pickUpType: string;
  /** 课程名称 */
  courseName: string;
  /** 班型备注 */
  courseRemark: string;
  /** 学车费用 */
  originalPrice: number;
  /** 优惠金额 */
  discountPrice: number;
  /** 学车时间,可用值:WORKDAY,MONDAY_TO_SUNDAY,WEEKEND,NIGHT */
  trainingTime: string;
  /** 收费模式,可用值:UNKNOWN,NORMAL,INSTALLMENT_SUPPORTED,CHARGE_BY_TIME,CHARGE_AFTER_TRAIN */
  chargeMode: string;
  /** 拿本时长,可用值:DAY2,DAY7,DAY15,DAY45,DAY60,DAY90,DAY180,DAY183 */
  takeDays: string;
  /** 约车方式,可用值:CONSULTATION,RESERVE_SPACE,PRIORITY,NO_NEED_APPOINTMENT,BY_SELF,APPOINTMENT_BY_JIAXIAO,BY_PHONE,ONLINE_APPOINTMENT */
  makeAppointment: string;
  /** 特殊服务,可用值:EMPTY,FREE_MAKE_UP_EXAMINATION,FREE_PEILIAN,VIP_SERVICE,JUST_CHARGE_ONCE,PERSONAL_TAILOR */
  specialService: string;
  /** 适合人群,可用值:COMMON,UNCERTAIN_TIME,LONG_LEARNING_CYCLE,FAST,THE_UPPER_CIRCLES,OFFICE_WORKERS,STUDENTS,WORKLESS,LADY */
  forTheCrowds: string;
  /** 科目二每车人数 */
  kemu2StudentsPerCar: number;
  /** 科目三每车人数 */
  kemu3StudentsPerCar: number;
  /**  */
  courseImage: CourseImageResponse;
  /** 班型详情图片信息 */
  courseDetailImages: CourseDetailImagesResponse[];
  /** 班型明细信息 */
  courseFeeDetails: CourseFeeDetailsResponse[];
}
export interface CourseListResponse {
  /**  */
  course: CourseResponse;
  /** 驾校简称 */
  jiaxiaoSimpleName: string;
}

/**
 * 下拉选项
 */
export interface OptionItem {
  label: string;
  value: string | number;
}
