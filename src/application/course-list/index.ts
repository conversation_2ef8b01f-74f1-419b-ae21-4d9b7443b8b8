import { defineComponent, ref, reactive, toRefs } from 'vue';
import { ModelController } from 'admin-library';
import { MESSAGE_TYPE, MUtils } from '@paas/paas-library';
import { COLUMNS } from './config';
import { GetCourseListStore, GetDriveLicenseStore, DisplayStatusStore } from './store';
import { StateModel } from './types';
import CourseListDialogComp from './components/course-list-dialog/index.vue';

export default defineComponent({
  components: {
    CourseListDialogComp
  },
  setup() {
    const state = reactive<StateModel>({
      driveLicenseList: [],
      driveLicenseTypes: null
    });

    // 常量
    const constants = {
      COLUMNS
    };

    // 控制器
    const controller = new ModelController({
      table: {
        store: GetCourseListStore
      }
      // search: {
      //   showStatus: {
      //     store: DisplayStatusStore
      //   },
      //   licenseType: {
      //     store: GetDriveLicenseStore
      //   }
      // }
    });

    // 组件引用
    const components = {
      courseListDialogRef: ref<InstanceType<typeof CourseListDialogComp>>()
    };

    // 方法
    const methods = {
      /**
       * 处理展示状态切换
       */
      // async handleDisplayStatusChange(record: any, checked: boolean) {
      //   MUtils.confirm({
      //     title: '提示',
      //     content: `确定修改展示状态吗?`,
      //     type: MESSAGE_TYPE.warning
      //   }).then(res => {
      //     console.log(res, '==========');
      //   });
      // },

      /**
       * 查看图片详情
       */
      viewImage(record: any) {
        components.courseListDialogRef.value?.open(record);
      },

      /**
       * 刷新表格数据
       */
      onRefresh() {
        controller.tableRequest();
      }
    };

    return {
      ...toRefs(state),
      ...constants,
      ...components,
      ...methods,
      controller
    };
  }
});
