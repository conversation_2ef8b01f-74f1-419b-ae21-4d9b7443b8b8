<template>
  <pm-effi :controller="controller">
    <pm-search class="padding-between">
      <pm-search-single
        :antdProps="{
          placeholder: '班型ID',
          type: 'number'
        }"
        data-index="id"
        xtype="INPUT"
      />

      <!-- <pm-search-single
        :antdProps="{
          placeholder: 'PaaSID'
        }"
        data-index="PaaSID"
        xtype="INPUT"
      />
      <pm-search-single
        :antdProps="{
          placeholder: '名称'
        }"
        data-index="courseName"
        xtype="INPUT"
      /> -->
      <pm-search-single
        :antdProps="{
          placeholder: '驾校ID',
          type: 'number'
        }"
        data-index="jiaxiaoId"
        xtype="INPUT"
      />
      <!-- <pm-search-single
        :antdProps="{
          placeholder: '驾校简称'
        }"
        data-index="jiaxiaoSimpleName"
        xtype="INPUT"
      />
      <pm-search-single
        :antdProps="{
          placeholder: '驾照类型',
          fieldNames: { label: 'desc', value: 'code' }
        }"
        data-index="licenseType"
        xtype="SELECT"
      />
      <pm-search-single
        :antdProps="{
          placeholder: '展示状态',
          fieldNames: { label: 'label', value: 'value' }
        }"
        data-index="showStatus"
        xtype="SELECT"
      /> -->
    </pm-search>

    <pm-table
      :columns="COLUMNS"
      :useCustomColumn="true"
      :sortNum="false"
      :operations-width="150"
      :operations-fixed="true"
      :antdProps="{
        pagination: false,
        bordered: true,
        size: 'small'
      }"
    >
      <!-- 展示状态 -->
      <!-- <template #showType="{ record }">
        <m-switch :checked="record.showType === 1" @change="checked => handleDisplayStatusChange(record, checked)" />
      </template> -->

      <!-- 图片 -->
      <template #courseImages="{ record }">
        <div v-if="record.courseImages?.length > 0 || record.courseDetailImages?.length > 0">
          <a href="javascript:void(0)" @click="viewImage(record)">查看</a>
        </div>
      </template>

      <!-- 审核状态 -->
      <template #auditStatus="{ record }">
        <span>{{ record.auditStatus.desc }}</span>
      </template>
    </pm-table>
  </pm-effi>
  <course-list-dialog-comp ref="courseListDialogRef" />
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less" scoped></style>
