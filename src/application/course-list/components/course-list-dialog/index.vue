<template>
  <pm-dialog v-model:visible="visible" title="查看图片" width="900px" :footer="null" @cancel="handleCancel">
    <div class="image-preview-container">
      <div v-for="(item, index) in courseImgList" :key="index" class="image-section">
        <h4 class="section-title" v-if="item.images">{{ item.title }}</h4>
        <div class="image-grid">
          <div v-for="(image, index) in item.images" :key="index" class="image-item" @click="previewImage(image)">
            <img :src="image" alt="班型主图" class="preview-image" />
          </div>
        </div>
      </div>
    </div>
  </pm-dialog>
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less" scoped></style>
