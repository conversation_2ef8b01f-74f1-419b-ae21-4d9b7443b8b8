import { defineComponent, ref, reactive, toRefs } from 'vue';
import { MESSAGE_TYPE, MUtils } from 'admin-library';
import { useOpenPreviewImgStore } from '@/pinia/open-preview-img';
import { TypeEnum } from '@/pinia/open-preview-img/constants';
import { CourseResponse } from '../../types';

export default defineComponent({
  setup() {
    const state = reactive({
      visible: false,
      courseImgList: []
    });

    const openPreviewImgStore = useOpenPreviewImgStore();

    const methods = {
      /**
       * 打开弹框
       */
      open(record: CourseResponse) {
        state.courseImgList = [
          {
            title: '班型主图',
            images: record.courseImages
          },
          {
            title: '班型详情图片',
            images: record.courseDetailImages
          }
        ];

        state.visible = true;
      },

      /**
       * 关闭弹框
       */
      handleCancel() {
        state.visible = false;
        state.courseImgList = [];
      },

      /**
       * 预览图片
       */
      previewImage(imageUrl: string) {
        if (!imageUrl) {
          MUtils.toast('图片地址无效', MESSAGE_TYPE.error);
          return;
        }

        openPreviewImgStore.open({
          url: imageUrl,
          type: TypeEnum.IMG,
          current: 0
        });
      }
    };

    return {
      ...toRefs(state),
      ...methods
    };
  }
});
