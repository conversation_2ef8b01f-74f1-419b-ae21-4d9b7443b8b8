import { TableColumn, ColumnXtype } from 'admin-library';
import { DISPLAY_STATUS_OPTIONS } from './constants';

export const COLUMNS: TableColumn[] = [
  {
    title: '班型ID',
    dataIndex: 'id',
    width: 100,
    fixed: 'left'
  },

  // {
  //   title: 'PaaSID',
  //   dataIndex: 'PaaSID',
  //   width: 100
  // },
  {
    title: '来源ID',
    dataIndex: 'sourceId',
    width: 100
  },
  {
    title: '来源类型',
    dataIndex: 'sourceType',
    width: 100,
    render: (value: number) => {
      return value.desc;
    }
  },
  {
    title: '名称',
    dataIndex: 'courseName',
    oneline: true,
    width: 150,
    fixed: 'left'
  },
  {
    title: '驾校ID',
    dataIndex: 'jiaxiaoId',
    width: 100
  },
  {
    title: '驾校简称',
    dataIndex: 'jiaxiaoSimpleName',
    oneline: true,
    width: 120
  },
  {
    title: '驾照类型',
    dataIndex: 'licenseType',
    width: 100
  },
  {
    title: '价格',
    dataIndex: 'originalPrice',
    width: 100,
    render: (value: number) => {
      return value ? `¥${value}` : '-';
    }
  },
  {
    title: '展示状态',
    dataIndex: 'showStatus',
    width: 100,
    render: (value: number) => {
      return value.desc;
    }
  },
  {
    title: '图片',
    dataIndex: 'courseImages',
    width: 100,
    xtype: ColumnXtype.CUSTOM
  },
  {
    title: '审核状态',
    dataIndex: 'auditStatus',
    width: 100,
    xtype: ColumnXtype.CUSTOM
  }
];
