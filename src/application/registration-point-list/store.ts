import { PaasListResponse } from 'admin-library';
import { Store } from '@simplex/simple-store';
import { RegistrationPointStatusLogStore } from '@/store/jiaxiao-admin';
import { RecruitSiteShowListStore } from '@/store/mclaren';
import { RegistrationPointListResponse, StatusLogResponse, OptionItem } from './types';
import { LEVEL_OPTIONS } from './constants';
import { SOURCE_ID_TYPE_OPTIONS, AUDIT_STATUS_OPTIONS, ChangeTypeEnum, CHANGE_TYPE_OPTIONS } from '@/utils/constants';

// 招生点列表
export const GetRegistrationPointListStore = new RecruitSiteShowListStore<
  PaasListResponse<RegistrationPointListResponse>
>({});

// 招生点状态变更日志
export const GetRegistrationPointStatusLogStore = new RegistrationPointStatusLogStore<
  PaasListResponse<StatusLogResponse>
>({});

// 级别下拉选项
export const LevelListStore = new Store(LEVEL_OPTIONS);

// 展示状态下拉选项
export const DisplayStatusStore = new Store(CHANGE_TYPE_OPTIONS);

// 来源类型下拉选项
export const SourceTypeStore = new Store(SOURCE_ID_TYPE_OPTIONS);
