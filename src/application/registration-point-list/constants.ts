import { OptionItem } from './types';

const enum LevelEnum {
  ONE = 1,
  TWO = 2,
  THREE = 3
}

// 级别选项
export const LEVEL_OPTIONS: OptionItem[] = [
  { value: LevelEnum.ONE, label: '一级' },
  { value: LevelEnum.TWO, label: '二级' },
  { value: LevelEnum.THREE, label: '三级' }
];

export const enum DisplayStatusEnum {
  SHOW = 1,
  HIDE = 0
}

// 展示状态选项
export const DISPLAY_STATUS_OPTIONS: OptionItem[] = [
  { value: DisplayStatusEnum.SHOW, label: '是' },
  { value: DisplayStatusEnum.HIDE, label: '否' }
];

// 禁用状态选项
export const BLOCK_STATUS_OPTIONS: OptionItem[] = [
  { value: DisplayStatusEnum.HIDE, label: '正常' },
  { value: DisplayStatusEnum.SHOW, label: '禁用' }
];

const enum AuditStatusEnum {
  /** 未提交 */
  NOSUBMIT = 0,
  /** 待提交 */
  PENDING = 1,
  /** 审核中 */
  REVIEW = 2,
  /** 审核通过 */
  APPROVED = 3,
  /** 审核失败 */
  ERR = 4
}

// 审核状态选项
export const AUDIT_STATUS_OPTIONS: OptionItem[] = [
  { value: AuditStatusEnum.NOSUBMIT, label: '未提交' },
  { value: AuditStatusEnum.PENDING, label: '待提交' },
  { value: AuditStatusEnum.REVIEW, label: '审核中' },
  { value: AuditStatusEnum.APPROVED, label: '审核通过' },
  { value: AuditStatusEnum.ERR, label: '审核失败' }
];

// 状态变更类型
export enum ChangeTypeEnum {
  DISPLAY = 1,
  BLOCK = 2
}

// 状态变更类型选项
export const CHANGE_TYPE_OPTIONS: OptionItem[] = [
  { value: ChangeTypeEnum.DISPLAY, label: '展示状态' },
  { value: ChangeTypeEnum.BLOCK, label: '禁用状态' }
];
