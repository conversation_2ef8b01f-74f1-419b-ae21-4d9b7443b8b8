import { TableColumn, ColumnXtype } from 'admin-library';
import { AuditStatusEnum, ChangeTypeEnum, SourceTypeEnum } from '@/utils/constants';
import { findText } from '@/utils/utils';
export const COLUMNS: TableColumn[] = [
  {
    title: '招生点Id',
    dataIndex: 'id',
    width: 100,
    fixed: 'left'
  },
  {
    title: '来源类型',
    dataIndex: 'sourceType',
    width: 100,
    render: (value: SourceTypeEnum) => {
      return value.desc;
    }
  },
  {
    title: '来源ID',
    dataIndex: 'sourceId',
    width: 100
  },
  // {
  //   title: '招生点名称',
  //   dataIndex: 'name',
  //   oneline: true,
  //   width: 150,
  //   fixed: 'left'
  // },
  // {
  //   title: '区县',
  //   dataIndex: 'cityName',
  //   width: 120
  // },
  {
    title: '驾校ID',
    dataIndex: 'jiaxiaoId',
    width: 100
  },
  {
    title: '驾校简称',
    dataIndex: 'jiaxiaoSimpleName',
    oneline: true,
    width: 120,
    xtype: ColumnXtype.CUSTOM
  },
  // {
  //   title: '级别',
  //   dataIndex: 'level',
  //   width: 80
  //   // xtype: ColumnXtype.CUSTOM
  // },
  // {
  //   title: '地址',
  //   dataIndex: 'address',
  //   width: 190,
  //   xtype: ColumnXtype.CUSTOM
  // },
  {
    title: '展示状态',
    dataIndex: 'showStatus',
    width: 100,
    render: (value: ChangeTypeEnum) => {
      return value.desc;
    }
  },
  // {
  //   title: '黑名单',
  //   dataIndex: 'hideType',
  //   width: 100
  //   // xtype: ColumnXtype.CUSTOM
  // },
  // {
  //   title: '图片',
  //   dataIndex: 'imageUrl',
  //   width: 100,
  //   xtype: ColumnXtype.CUSTOM
  // },
  {
    title: '审核状态',
    dataIndex: 'auditStatus',
    width: 100,
    render: (value: AuditStatusEnum) => {
      return value.desc;
    }
  }
];
