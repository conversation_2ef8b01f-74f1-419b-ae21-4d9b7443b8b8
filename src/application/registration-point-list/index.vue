<template>
  <pm-effi :controller="controller">
    <pm-search class="padding-between">
      <pm-search-single
        :antdProps="{
          placeholder: '招生点Id',
          type: 'number'
        }"
        data-index="id"
        xtype="INPUT"
      />

      <!-- <pm-search-single
        :antdProps="{
          placeholder: '来源类型',
          fieldNames: { label: 'label', value: 'value' }
        }"
        data-index="sourceType"
        xtype="SELECT"
      />
    
      <pm-search-single
        :antdProps="{
          placeholder: '招生点名称'
        }"
        data-index="name"
        xtype="INPUT"
      />
      -->
      <pm-search-single
        :antdProps="{
          placeholder: '驾校ID',
          type: 'number'
        }"
        data-index="jiaxiaoId"
        xtype="INPUT"
      />
      <pm-search-single
        :antdProps="{
          placeholder: '来源ID'
        }"
        data-index="sourceId"
        xtype="INPUT"
      />
      <!-- <pm-search-single
        :antdProps="{
          placeholder: '驾校简称'
        }"
        data-index="jiaxiaoName"
        xtype="INPUT"
      />
      <pm-search-single :span="4" data-index="cityCode" xtype="CUSTOM">
        <template #custom>
          <m-cascader
            v-model:value="cityCode"
            :options="cityDataStore.data"
            change-on-select
            :allowClear="false"
            placeholder="区县"
          />
        </template>
      </pm-search-single>
      <pm-search-single
        :antdProps="{
          placeholder: '级别',
          fieldNames: { label: 'label', value: 'value' }
        }"
        data-index="level"
        xtype="SELECT"
      />
      <pm-search-single
        :antdProps="{
          placeholder: '展示状态',
          fieldNames: { label: 'label', value: 'value' }
        }"
        data-index="showType"
        xtype="SELECT"
      /> -->
    </pm-search>

    <pm-table
      :columns="COLUMNS"
      :useCustomColumn="true"
      :sortNum="false"
      :operations-width="150"
      :operations-fixed="true"
      :antdProps="{
        pagination: false,
        bordered: true,
        size: 'small'
      }"
    >
      <!-- 级别 -->
      <!-- <template #level="{ record }">
        <m-tag :color="getLevelTagColor(record.level)">
          {{ getLevelText(record.level) }}
        </m-tag>
      </template> -->

      <template #simpleName="{ record }">
        <div class="oneline maxwidth">{{ record.simpleName }}</div>
      </template>

      <!-- 地址 -->
      <template #address="{ record }">
        <m-tooltip :title="record.address" placement="top">
          <m-button type="link" class="oneline maxwidth" @click="onMap(record)">
            {{ record.address }}
          </m-button>
        </m-tooltip>
      </template>

      <!-- 展示状态 -->
      <!-- <template #showType="{ record }">
        <m-switch
          :checked="record.showType === DisplayStatusEnum.SHOW"
          @change="handleDisplayStatusChange(record, checked)"
        />
      </template> -->

      <!-- 黑名单 -->
      <!-- <template #blackStatus="{ record }">
        <m-switch
          :checked="record.blackStatus === DisplayStatusEnum.SHOW"
          @change="handleBlackStatusChange(record, checked)"
        />
      </template> -->

      <!-- 图片 -->
      <template #imageUrl="{ record }">
        <m-button v-if="record.imageUrl" type="link" @click="viewImage(record.imageUrl)">查看图片</m-button>
        <span v-else>-</span>
      </template>
    </pm-table>
  </pm-effi>

  <!-- 状态日志对话框 -->
  <a-map-comp :ref="aMapStore?.aMapRef" />
  <!-- 素材弹窗 -->
  <materials-dialog ref="materialsDialogRef" />
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less" scoped></style>
