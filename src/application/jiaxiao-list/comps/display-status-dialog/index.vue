<template>
  <pm-dialog v-model:visible="visible" title="修改展示状态" width="500px" @close="handleCancel">
    <m-form ref="formRef" :model="formData" label-width="120px">
      <m-form-item label="展示状态" v-bind="validateInfos.applyShowStatus">
        <m-select
          v-model:value="formData.applyShowStatus"
          placeholder="请选择展示状态"
          style="width: 100%"
          @change="handleDisplayStatusChange"
        >
          <m-select-option v-for="option in DISPLAY_STATUS_OPTIONS" :key="option.value" :value="option.value">
            {{ option.label }}
          </m-select-option>
        </m-select>
      </m-form-item>
      <m-form-item label="下架原因" v-bind="validateInfos.showChangeRemarkCode">
        <m-select v-model:value="formData.showChangeRemarkCode" placeholder="请选择下架原因" style="width: 100%">
          <m-select-option v-for="option in reasonList" :key="option.uuid" :value="option.uuid">
            {{ option.reason }}
          </m-select-option>
        </m-select>
      </m-form-item>
    </m-form>
    <template #footer>
      <m-button key="back" @click="handleCancel">取消</m-button>
      <m-button key="submit" type="primary" :loading="loading" @click="handleSubmit">保存</m-button>
    </template>
  </pm-dialog>
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less" scoped></style>
