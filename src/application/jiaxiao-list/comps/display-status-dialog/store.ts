import { PaasListResponse } from 'admin-library';
import { GetShowRuleConfigStore, GetShowDataStore, ApplyShowStore } from '@/store/mclaren';
import { ShowRuleConfigResponse, ShowDataResponse } from './type';

// 获取驾校展示变更原因
export const GetShowConfigStore = new GetShowRuleConfigStore<PaasListResponse<ShowRuleConfigResponse>>({});

// 获取驾校当前所处于的状态
export const GetJiaXiaoShowDataStore = new GetShowDataStore<ShowDataResponse>({});

// 展示状态变更申请
export const GetApplyShowStore = new ApplyShowStore<{ value: boolean }>({});
