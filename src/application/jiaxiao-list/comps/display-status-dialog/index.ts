import { defineComponent, ref, reactive, toRefs } from 'vue';
import { MESSAGE_TYPE, MUtils } from '@paas/paas-library';
import { JiaxiaoListResponse } from '../../types';
import { DISPLAY_STATUS_OPTIONS, DISPLAY_STATUS } from './constants';
import { GetShowConfigStore, GetJiaXiaoShowDataStore, GetApplyShowStore } from './store';
import { Form } from 'ant-design-vue';

export default defineComponent({
  emits: ['success'],
  setup(props, { emit }) {
    const useForm = Form.useForm;

    const state = reactive({
      displayStatusOptions: DISPLAY_STATUS_OPTIONS,
      formData: {
        applyShowStatus: DISPLAY_STATUS.SHOW,
        showChangeRemarkCode: null
      },
      visible: false,
      reasonList: [],
      jiaxiaoId: null
    });

    const constants = {
      DISPLAY_STATUS_OPTIONS,
      DISPLAY_STATUS
    };

    const methods = {
      /**
       * 打开弹框
       */
      open(record: JiaxiaoListResponse) {
        state.visible = true;
        methods.getJiaXiaoShowData(record.jiaxiaoId);
      },

      /**
       * 获取变更原因
       */
      async getShowChangeRemarkCode(showStatus) {
        const res = await GetShowConfigStore.request({ showStatus }).getData();
        state.reasonList = res.itemList;
      },

      /**
       * 获取驾校当前状态
       */
      getJiaXiaoShowData(id) {
        state.jiaxiaoId = id;
        GetJiaXiaoShowDataStore.request({ jiaxiaoId: id })
          .getData()
          .then(res => {
            methods.getShowChangeRemarkCode(res?.showStatus);
            state.formData.applyShowStatus = res?.showStatus;
            state.formData.showChangeRemarkCode = res?.showChangeRemarkCode ? String(res?.showChangeRemarkCode) : null;
          });
      },

      /**
       * 提交表单
       */
      async handleSubmit() {
        await validate();

        const params = {
          jiaxiaoId: state.jiaxiaoId,
          showStatus: state.formData.applyShowStatus,
          reasonUUID: state.formData.showChangeRemarkCode
        };

        await GetApplyShowStore.request(params);

        MUtils.toast('修改成功', MESSAGE_TYPE.success);
        state.visible = false;
        emit('success');
      },

      async handleDisplayStatusChange(value) {
        await methods.getShowChangeRemarkCode(value);
        state.formData.showChangeRemarkCode = state.reasonList[0]?.uuid;
      },

      /**
       * 取消操作
       */
      handleCancel: () => {
        state.visible = false;
        resetFields();
      }
    };

    const rules = {
      applyShowStatus: [{ required: true, message: '请选择展示状态', trigger: 'change' }],
      showChangeRemarkCode: [{ required: true, message: '请选择下架原因', trigger: 'change' }]
    };

    // 表单校验
    const { validate, validateInfos, resetFields } = useForm(state.formData, rules);

    return {
      ...toRefs(state),
      ...methods,
      ...constants,
      validateInfos
    };
  }
});
