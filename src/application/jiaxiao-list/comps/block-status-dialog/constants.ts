export const BLOCK_STATUS = {
  // 正常
  NORMAL: 0,
  // 屏蔽部分页面
  BLOCK_PAGE: 1,
  // 屏蔽部分功能
  BLOCK_FUNCTION: 2,
  // 屏蔽留资功能
  BLOCK_LEAD_FUNCTION: 3
};

export const BLOCK_STATUS_OPTIONS = [
  {
    label: '正常',
    value: BLOCK_STATUS.NORMAL
  },
  {
    label: '屏蔽部分页面',
    value: BLOCK_STATUS.BLOCK_PAGE
  },
  {
    label: '屏蔽部分功能',
    value: BLOCK_STATUS.BLOCK_FUNCTION
  },
  {
    label: '屏蔽留资功能',
    value: BLOCK_STATUS.BLOCK_LEAD_FUNCTION
  }
];

// 屏蔽原因选项映射
export const BLOCK_REASON_MAP = {
  [BLOCK_STATUS.BLOCK_PAGE]: [
    // 屏蔽部分页面
    { value: '1', label: '页面违规内容' },
    { value: '2', label: '页面信息不实' },
    { value: '3', label: '页面存在安全隐患' }
  ],
  [BLOCK_STATUS.BLOCK_FUNCTION]: [
    // 屏蔽部分功能
    { value: '1', label: '功能存在bug' },
    { value: '2', label: '功能未完善' },
    { value: '3', label: '功能存在风险' }
  ],
  [BLOCK_STATUS.BLOCK_LEAD_FUNCTION]: [
    // 屏蔽留资功能
    { value: '1', label: '留资信息虚假' },
    { value: '2', label: '留资流程异常' },
    { value: '3', label: '留资存在风险' }
  ]
};
