import { PaasListResponse } from 'admin-library';
import { HideRuleConfigStore, GetShowDataStore, ApplyHideStore } from '@/store/mclaren';
import { ShowRuleConfigResponse, ShowDataResponse } from './type';

// 获取驾校屏蔽变更原因
export const GetHideRuleConfigStore = new HideRuleConfigStore<PaasListResponse<ShowRuleConfigResponse>>({});

// 获取驾校当前所处于的状态
export const GetJiaXiaoShowDataStore = new GetShowDataStore<ShowDataResponse>({});

// 展示状态变更申请
export const GetApplyHideStore = new ApplyHideStore<{ value: boolean }>({});
