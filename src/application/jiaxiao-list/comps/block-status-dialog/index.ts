import { defineComponent, ref, reactive, computed, toRefs } from 'vue';
import { MESSAGE_TYPE, MUtils } from '@paas/paas-library';
import { JiaxiaoListResponse, OptionItem } from '../../types';
import { BLOCK_STATUS_OPTIONS, BLOCK_REASON_MAP, BLOCK_STATUS } from './constants';
import { GetHideRuleConfigStore, GetJiaXiaoShowDataStore, GetApplyHideStore } from './store';
import { Form } from 'ant-design-vue';

export default defineComponent({
  emits: ['success'],
  setup(props, { emit }) {
    const useForm = Form.useForm;
    const state = reactive({
      blockStatusOptions: [],
      visible: false,
      currentRecord: null,
      formData: {
        blockStatus: BLOCK_STATUS.NORMAL,
        blockReason: ''
      }
    });

    const constants = {
      BLOCK_STATUS_OPTIONS,
      BLOCK_REASON_MAP,
      BLOCK_STATUS
    };

    /**
     * 根据屏蔽状态获取对应的屏蔽原因选项
     */
    const blockReasonOptions = computed((): OptionItem[] => {
      return BLOCK_REASON_MAP[state.formData.blockStatus] || [];
    });

    const rules = {
      blockStatus: [{ required: true, message: '请选择屏蔽状态', trigger: 'change' }],
      blockReason: [{ required: true, message: '请选择屏蔽原因', trigger: 'change' }]
    };

    // 表单校验
    const { validate, validateInfos, resetFields } = useForm(state.formData, rules);

    const methods = {
      /**
       * 打开弹框
       */
      open: (record: JiaxiaoListResponse) => {
        state.visible = true;
        methods.getJiaXiaoShowData(record.jiaxiaoId);
      },

      /**
       * 获取驾校当前状态
       */
      getJiaXiaoShowData(id) {
        state.jiaxiaoId = id;
        GetJiaXiaoShowDataStore.request({ jiaxiaoId: id })
          .getData()
          .then(res => {
            methods.getHideChangeRemarkCode(res?.hideType);
            state.formData.blockStatus = res?.hideType;
            state.formData.blockReason = res?.hideChangeRemarkCode ? String(res?.hideChangeRemarkCode) : null;
          });
      },
      /**
       * 获取变更原因
       */
      async getHideChangeRemarkCode(hideType) {
        const res = await GetHideRuleConfigStore.request({ hideType: hideType }).getData();
        state.blockStatusOptions = res.itemList;
      },

      async handleBlockStatusChange(value) {
        await methods.getHideChangeRemarkCode(value);
        state.formData.blockReason = state.blockStatusOptions[0]?.uuid;
      },

      /**
       * 提交表单
       */
      handleSubmit() {
        validate().then(() => {
          const params = {
            jiaxiaoId: state.jiaxiaoId,
            hideType: state.formData.blockStatus,
            reasonUUID: state.formData.blockReason
          };

          GetApplyHideStore.request(params)
            .getData()
            .then(() => {
              MUtils.toast('修改成功', MESSAGE_TYPE.success);
              state.visible = false;
              emit('success');
            });
        });
      },

      /**
       * 取消操作
       */
      handleCancel: () => {
        state.visible = false;
        resetFields();
      }
    };

    return {
      ...toRefs(state),
      blockReasonOptions,
      validateInfos,
      ...constants,
      ...methods
    };
  }
});
