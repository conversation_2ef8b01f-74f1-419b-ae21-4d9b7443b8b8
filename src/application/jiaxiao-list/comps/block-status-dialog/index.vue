<template>
  <pm-dialog v-model:visible="visible" title="修改屏蔽状态" width="500px" @close="handleCancel">
    <m-form ref="formRef" :model="formData" label-width="120px">
      <m-form-item label="屏蔽状态" v-bind="validateInfos.blockStatus">
        <m-select
          v-model:value="formData.blockStatus"
          placeholder="请选择屏蔽状态"
          style="width: 100%"
          @change="handleBlockStatusChange"
        >
          <m-select-option v-for="option in BLOCK_STATUS_OPTIONS" :key="option.value" :value="option.value">
            {{ option.label }}
          </m-select-option>
        </m-select>
      </m-form-item>

      <m-form-item label="屏蔽原因" v-bind="validateInfos.blockReason">
        <m-select v-model:value="formData.blockReason" placeholder="请选择屏蔽原因" style="width: 100%">
          <m-select-option v-for="option in blockStatusOptions" :key="option.uuid" :value="option.uuid">
            {{ option.reason }}
          </m-select-option>
        </m-select>
      </m-form-item>
    </m-form>
    <template #footer>
      <m-button key="back" @click="handleCancel">取消</m-button>
      <m-button key="submit" type="primary" @click="handleSubmit">保存</m-button>
    </template>
  </pm-dialog>
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less" scoped></style>
