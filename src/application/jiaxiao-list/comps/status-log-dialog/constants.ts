export enum TypeEnum {
  /** 上线 */
  ONLINE = 10,
  /** 下线 */
  OFFLINE = 11,
  /** 修改下架原因 */
  MODIFY_REASON = 12,
  /** 取消屏蔽 */
  CANCEL_BLOCK = 20,
  /** 屏蔽部分页面 */
  BLOCK_PAGE = 21,
  /** 屏蔽部分功能 */
  BLOCK_FUNCTION = 22,
  /** 屏蔽留资功能 */
  BLOCK_LEAD = 23,
  /** 装修驾校信息 */
  DECORATE_INFO = 30
}

/**
 * 操作类型
 */
export const OPERATE_TYPE = [
  {
    label: '上线',
    value: TypeEnum.ONLINE
  },
  {
    label: '下线',
    value: TypeEnum.OFFLINE
  },
  {
    label: '修改下架原因',
    value: TypeEnum.MODIFY_REASON
  },
  {
    label: '取消屏蔽',
    value: TypeEnum.CANCEL_BLOCK
  },
  {
    label: '屏蔽部分页面',
    value: TypeEnum.BLOCK_PAGE
  },
  {
    label: '屏蔽部分功能',
    value: TypeEnum.BLOCK_FUNCTION
  },
  {
    label: '屏蔽留资功能',
    value: TypeEnum.BLOCK_LEAD
  }
];

export const CHANGE_TYPE = {
  SHOW_STATUS: 'SHOW_STATUS',
  HIDE_TYPE: 'HIDE_TYPE'
};

export const TITLE_MAP = {
  [CHANGE_TYPE.SHOW_STATUS]: '展示状态变更日志',
  [CHANGE_TYPE.HIDE_TYPE]: '屏蔽状态变更日志'
};

/**
 * 操作类型
 */
export const OPERATE_TYPE_MAP = {
  [CHANGE_TYPE.SHOW_STATUS]: [
    {
      label: '上线',
      value: TypeEnum.ONLINE
    },
    {
      label: '下线',
      value: TypeEnum.OFFLINE
    },
    {
      label: '修改下架原因',
      value: TypeEnum.MODIFY_REASON
    }
  ],
  [CHANGE_TYPE.HIDE_TYPE]: [
    {
      label: '取消屏蔽',
      value: TypeEnum.CANCEL_BLOCK
    },
    {
      label: '屏蔽部分页面',
      value: TypeEnum.BLOCK_PAGE
    },
    {
      label: '屏蔽部分功能',
      value: TypeEnum.BLOCK_FUNCTION
    },
    {
      label: '屏蔽留资功能',
      value: TypeEnum.BLOCK_LEAD
    }
  ]
};
