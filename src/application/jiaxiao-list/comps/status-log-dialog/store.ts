import { PaasListResponse } from 'admin-library';
import { GetShowChangeLogListStore, GetShowRuleConfigStore, ChangeReasonListStore } from '@/store/mclaren';
import { listResponse } from './type';
import { ShowRuleConfigResponse } from '../display-status-dialog/type';
//变更列表
export const GetListStore = new GetShowChangeLogListStore<PaasListResponse<listResponse>>({});

// 获取驾校展示变更原因
export const GetShowConfigStore = new GetShowRuleConfigStore<PaasListResponse<ShowRuleConfigResponse>>({});

// 变更原因下拉选项
export const GetChangeReasonListStore = new ChangeReasonListStore<PaasListResponse<listResponse>>({});
