<template>
  <pm-dialog v-model:visible="visible" :title="title" centered width="1000px" @close="onCancel" :footer="null">
    <pm-effi :controller="controller">
      <pm-search>
        <pm-search-single>
          <template #custom>
            <m-select v-model:value="operateCode" placeholder="操作类型">
              <m-select-option v-for="item in operateList" :key="item.value" :label="item.label" :value="item.value">
                {{ item.label }}
              </m-select-option>
            </m-select>
          </template>
        </pm-search-single>
        <pm-search-single :span="12">
          <template #custom>
            <m-select v-model:value="remarkCode" placeholder="下架原因">
              <m-select-option v-for="item in reasonList" :key="item.uuid" :label="item.reason" :value="item.uuid">
                {{ item.reason }}
              </m-select-option>
            </m-select>
          </template>
        </pm-search-single>
      </pm-search>
      <pm-table
        :columns="COLUMNS"
        :useCustomColumn="true"
        :sortNum="false"
        :antdProps="{
          pagination: false,
          bordered: true,
          size: 'small'
        }"
      ></pm-table>
    </pm-effi>
  </pm-dialog>
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less" scoped></style>
