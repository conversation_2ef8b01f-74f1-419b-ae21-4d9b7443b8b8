import { defineComponent, reactive, toRefs, computed } from 'vue';
import { Model<PERSON>ontroller, BTN_RESET } from 'admin-library';
import { StateModel } from './types';
import { COLUMNS } from './config';
import { GetListStore, GetChangeReasonListStore } from './store';
import { OPERATE_TYPE_MAP, CHANGE_TYPE, TITLE_MAP } from './constants';
export default defineComponent({
  setup() {
    const state = reactive<StateModel>({
      visible: false,
      jiaxiaoId: null,
      changeType: null,
      reasonList: [],
      operateCode: null,
      remarkCode: null
    });

    // 常量
    const constants = {
      COLUMNS,
      OPERATE_TYPE_MAP,
      CHANGE_TYPE,
      TITLE_MAP
    };

    // 控制器
    const controller = new ModelController({
      table: {
        store: GetListStore
      }
    });

    controller.table.onRequest.use(params => {
      params.jiaxiaoId = state.jiaxiaoId;
      params.changeType = state.changeType;
      params.operateCode = state.operateCode;
      params.remarkCode = state.remarkCode;
      return params;
    });

    controller.listener('reset', () => {
      state.operateCode = null;
      state.remarkCode = null;
    });

    const operateList = computed(() => {
      return OPERATE_TYPE_MAP[state.changeType];
    });

    const title = computed(() => {
      return TITLE_MAP[state.changeType];
    });

    // 方法
    const methods = {
      /**
       * 打开弹窗
       */
      open(record: any, changeType?: string) {
        state.visible = true;
        state.jiaxiaoId = record.jiaxiaoId;
        state.changeType = changeType;
        methods.getShowChangeRemarkCode();
        controller.tableRequest();
      },

      /**
       * 获取变更原因
       */
      async getShowChangeRemarkCode() {
        const res = await GetChangeReasonListStore.request({ changeType: state.changeType }).getData();
        state.reasonList = res.itemList;
      },

      /**
       * 关闭弹窗
       */
      onCancel() {
        state.visible = false;
        state.jiaxiaoId = null;
        state.changeType = null;
        state.operateCode = null;
        state.remarkCode = null;
      }
    };

    return {
      ...toRefs(state),
      ...constants,
      ...methods,
      controller,
      operateList,
      title
    };
  }
});
