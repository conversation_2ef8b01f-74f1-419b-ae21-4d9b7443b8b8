import { TableColumn, TableDateFormat, ColumnXtype } from 'admin-library';
import { OPERATE_TYPE } from './constants';
import { findText } from '@/utils/utils';
export const COLUMNS: TableColumn[] = [
  {
    title: '驾校ID',
    dataIndex: 'jiaxiaoId',
    width: 100
  },
  {
    title: '操作类型',
    dataIndex: 'operateCode',
    width: 120,
    render: (value: number) => {
      return findText(OPERATE_TYPE, value);
    }
  },
  {
    title: '下架原因',
    dataIndex: 'remark',
    width: 200
  },

  {
    title: '操作人',
    dataIndex: 'createUserName',
    width: 100
  },
  {
    title: '变更时间',
    dataIndex: 'updateTime',
    dateFormat: TableDateFormat.SECONDS,
    width: 130
  }
];
