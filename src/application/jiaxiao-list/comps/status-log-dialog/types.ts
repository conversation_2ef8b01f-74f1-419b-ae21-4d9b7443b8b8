export interface StateModel {
  visible: boolean;
  // 驾校ID
  jiaxiaoId: number;
  // 变更类型
  changeType: string;
  // 变更原因列表
  reasonList: any[];
  // 操作类型
  operateCode: number;
  // 变更原因
  remarkCode: number;
}
export interface listResponse {
  jiaxiaoId: number;
  // 驾校展示变更分类
  changeType: string;
  //备注说明文本对应的编码
  remarkCode: number;
  //备注信息
  remark: string;
  //操作类型
  operateCode: number;
  operateUserId: string;
  operateUserName: string;
  //操作时间
  operateTime: string;
  createUserId: string;
  createUserName: string;
  createTime: string;
  updateUserName: string;
  updateTime: string;
}
