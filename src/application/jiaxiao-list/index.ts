import { defineComponent, reactive, ref, toRefs } from 'vue';
import { ModelController } from 'admin-library';
import StatusLogDialog from './comps/status-log-dialog/index.vue';
import MaterialsDialog from '@/components/materials-dialog/index.vue';
import DisplayStatusDialog from './comps/display-status-dialog/index.vue';
import BlockStatusDialog from './comps/block-status-dialog/index.vue';
import { COLUMNS } from './config';
import { GetJiaxiaoListStore } from './store';
import { useCityDataStore } from '@/pinia/city-data';
import { findText } from '@/utils/utils';
import { BLOCK_STATUS_OPTIONS } from '@/application/jiaxiao-list/constants';

export default defineComponent({
  components: {
    StatusLogDialog,
    MaterialsDialog,
    DisplayStatusDialog,
    BlockStatusDialog
  },
  setup() {
    const cityDataStore = useCityDataStore();

    // 常量
    const constants = {
      COLUMNS,
      BLOCK_STATUS_OPTIONS,
      cityDataStore
    };

    const state = reactive({
      cityCode: [],
      cityOptions: []
    });

    // 控制器
    const controller = new ModelController({
      table: {
        store: GetJiaxiaoListStore
      }
      // search: {
      //   level: {
      //     store: LevelListStore
      //   },
      //   displayStatus: {
      //     store: DisplayStatusStore
      //   },
      //   blockStatus: {
      //     store: BlockStatusStore
      //   },
      //   settleStatus: {
      //     store: SettleStatusStore
      //   },
      //   basicAuth: {
      //     store: BasicAuthStore
      //   }
      // }
    });

    controller.table.onRequest.use(params => {
      console.log(state.cityCode, 'state.cityCode');
      params.cityCode = state.cityCode.join(',');
      return params;
    });

    // 组件引用
    const components = {
      statusLogDialogRef: ref<InstanceType<typeof StatusLogDialog>>(),
      materialsDialogRef: ref<InstanceType<typeof MaterialsDialog>>(),
      displayStatusDialogRef: ref<InstanceType<typeof DisplayStatusDialog>>(),
      blockStatusDialogRef: ref<InstanceType<typeof BlockStatusDialog>>()
    };

    // 方法
    const methods = {
      /**
       * 打开修改展示状态弹框
       */
      openDisplayStatusDialog(record: any) {
        components.displayStatusDialogRef.value?.open(record);
      },

      /**
       * 打开修改屏蔽状态弹框
       */
      openBlockStatusDialog(record: any) {
        components.blockStatusDialogRef.value?.open(record);
      },

      /**
       * 查看展示状态变更日志
       */
      viewDisplayStatusLog(record: any) {
        components.statusLogDialogRef.value?.open(record, 'SHOW_STATUS');
      },

      /**
       * 查看屏蔽状态变更日志
       */
      viewBlockStatusLog(record: any) {
        components.statusLogDialogRef.value?.open(record, 'HIDE_TYPE');
      },

      /**
       * 查看素材
       */
      viewMaterials(record: any) {
        components.materialsDialogRef.value?.open(record.jiaxiaoId);
      },

      /**
       * 刷新表格数据
       */
      onRefresh() {
        controller.tableRequest();
      },

      findText
    };

    return {
      ...toRefs(state),
      ...constants,
      ...components,
      ...methods,
      controller
    };
  }
});
