import { OptionItem } from './types';

const enum LevelEnum {
  ONE = 1,
  TWO = 2,
  THREE = 3
}

// 级别选项
export const LEVEL_OPTIONS: OptionItem[] = [
  { value: LevelEnum.ONE, label: '一级' },
  { value: LevelEnum.TWO, label: '二级' },
  { value: LevelEnum.THREE, label: '三级' }
];

const enum DisplayStatusEnum {
  SHOW = 1,
  HIDE = 0
}

// 展示状态选项
export const DISPLAY_STATUS_OPTIONS: OptionItem[] = [
  { value: DisplayStatusEnum.SHOW, label: '是' },
  { value: DisplayStatusEnum.HIDE, label: '否' }
];

// 屏蔽状态选项
export const BLOCK_STATUS_OPTIONS: OptionItem[] = [
  { value: 0, label: '正常' },
  { value: 1, label: '屏蔽部分页面' },
  { value: 2, label: '屏蔽部分功能' },
  { value: 3, label: '屏蔽留资功能' }
];

// 入驻状态选项
export const SETTLE_STATUS_OPTIONS: OptionItem[] = [
  { value: DisplayStatusEnum.HIDE, label: '未入驻' },
  { value: DisplayStatusEnum.SHOW, label: '已入驻' }
];

// 基础认证选项
export const BASIC_AUTH_OPTIONS: OptionItem[] = [
  { value: DisplayStatusEnum.HIDE, label: '未认证' },
  { value: DisplayStatusEnum.SHOW, label: '已认证' }
];

// 状态变更类型
export enum ChangeTypeEnum {
  DISPLAY = 1,
  BLOCK = 2
}

// 状态变更类型选项
export const CHANGE_TYPE_OPTIONS: OptionItem[] = [
  { value: ChangeTypeEnum.DISPLAY, label: '展示状态' },
  { value: ChangeTypeEnum.BLOCK, label: '屏蔽状态' }
];
