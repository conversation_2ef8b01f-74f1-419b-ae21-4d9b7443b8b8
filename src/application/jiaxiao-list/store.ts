import { PaasListResponse } from 'admin-library';
import { Store } from '@simplex/simple-store';
import { StatusLogStore, UpdateDisplayStatusStore, UpdateBlockStatusStore } from '@/store/jiaxiao-admin';

import { JiaxiaoListStore } from '@/store/mclaren';

import { JiaxiaoListResponse, StatusLogResponse, OptionItem } from './types';
import {
  LEVEL_OPTIONS,
  DISPLAY_STATUS_OPTIONS,
  BLOCK_STATUS_OPTIONS,
  SETTLE_STATUS_OPTIONS,
  BASIC_AUTH_OPTIONS
} from './constants';

// 驾校列表
export const GetJiaxiaoListStore = new JiaxiaoListStore<PaasListResponse<JiaxiaoListResponse>>({});

// 状态变更日志
export const GetStatusLogStore = new StatusLogStore<PaasListResponse<StatusLogResponse>>({});

// 更新展示状态
export const GetUpdateDisplayStatusStore = new UpdateDisplayStatusStore<PaasListResponse<JiaxiaoListResponse>>({});

// 更新屏蔽状态
export const GetUpdateBlockStatusStore = new UpdateBlockStatusStore<PaasListResponse<JiaxiaoListResponse>>({});

// 级别下拉选项
export const LevelListStore = new Store(LEVEL_OPTIONS);

// 展示状态下拉选项
export const DisplayStatusStore = new Store(DISPLAY_STATUS_OPTIONS);

// 屏蔽状态下拉选项
export const BlockStatusStore = new Store(BLOCK_STATUS_OPTIONS);

// 入驻状态下拉选项
export const SettleStatusStore = new Store(SETTLE_STATUS_OPTIONS);

// 基础认证下拉选项
export const BasicAuthStore = new Store(BASIC_AUTH_OPTIONS);
