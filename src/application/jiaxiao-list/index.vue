<template>
  <div class="jiaxiao-list">
    <pm-effi :controller="controller">
      <pm-search class="padding-between">
        <pm-search-single
          :antdProps="{
            placeholder: '驾校ID',
            type: 'number'
          }"
          data-index="jiaxiaoId"
          xtype="INPUT"
        />
        <pm-search-single
          :antdProps="{
            placeholder: '驾校简称'
          }"
          data-index="simpleName"
          xtype="INPUT"
        />
        <!-- <pm-search-single
          :antdProps="{
            placeholder: '驾校全称'
          }"
          data-index="fullName"
          xtype="INPUT"
        />
        <pm-search-single
          :antdProps="{
            placeholder: 'USCC'
          }"
          data-index="uscc"
          xtype="INPUT"
        />
        <pm-search-single :span="4" data-index="cityCode" xtype="CUSTOM">
          <template #custom>
            <m-cascader
              v-model:value="cityCode"
              :options="cityDataStore.data"
              :allowClear="false"
              change-on-select
              placeholder="区县"
            />
          </template>
        </pm-search-single>
        <pm-search-single
          :antdProps="{
            placeholder: '级别',
            fieldNames: { label: 'label', value: 'value' }
          }"
          data-index="level"
          xtype="SELECT"
        />
        <pm-search-single
          :antdProps="{
            placeholder: '展示状态',
            fieldNames: { label: 'label', value: 'value' }
          }"
          data-index="displayStatus"
          xtype="SELECT"
        />
        <pm-search-single
          :antdProps="{
            placeholder: '屏蔽状态',
            fieldNames: { label: 'label', value: 'value' }
          }"
          data-index="blockStatus"
          xtype="SELECT"
        />
        <pm-search-single
          :antdProps="{
            placeholder: '入驻状态',
            fieldNames: { label: 'label', value: 'value' }
          }"
          data-index="settleStatus"
          xtype="SELECT"
        />
        <pm-search-single
          :antdProps="{
            placeholder: '基础认证',
            fieldNames: { label: 'label', value: 'value' }
          }"
          data-index="basicAuth"
          xtype="SELECT"
        /> -->
      </pm-search>

      <pm-table
        :columns="COLUMNS"
        :useCustomColumn="true"
        :sortNum="false"
        :operations-width="150"
        :operations-fixed="true"
        :antdProps="{
          pagination: false,
          bordered: true,
          size: 'small'
        }"
      >
        <!-- 驾校状态 -->
        <template #status="{ record }">
          <div>入驻: {{ record.registered ? '是' : '否' }}</div>
          <div>基础认证: {{ record.baseInfoCertified ? '是' : '否' }}</div>
          <div>展示状态: {{ record.showStatus == 1 ? '展示' : '不展示' }}</div>
          <div>屏蔽状态: {{ findText(BLOCK_STATUS_OPTIONS, record.hideType) }}</div>
          <div>蓝V: {{ record.blueIconCertification === true ? '有' : '无' }}</div>
        </template>

        <!-- 驾校信息 -->
        <template #info="{ record }">
          <div>uscc: {{ record.usccCode }}</div>
          <div>级别: {{ record.jxCategory }}</div>
          <div>城市: {{ record.areaDesc }}</div>
        </template>

        <!-- 点评统计 -->
        <template #reviews="{ record }">
          <div>评分: {{ record.dianpingScore || 0 }}</div>
          <div>数量: {{ record.dianpingCount || 0 }}</div>
        </template>

        <!-- 人员统计 -->
        <template #staff="{ record }">
          <div>教练数: {{ record.coachCount || 0 }}</div>
          <div>学员数: {{ record.studentCount || 0 }}</div>
        </template>

        <!-- 场地统计 -->
        <template #venue="{ record }">
          <div>训练场: {{ record.trainFieldCount || 0 }}</div>
          <div>招生点: {{ record.recruitSiteCount || 0 }}</div>
          <div>考场: {{ record.examFieldCount || 0 }}</div>
        </template>

        <!-- 素材 -->
        <template #materials="{ record }">
          <a href="javascript:void(0)" @click="viewMaterials(record)">查看</a>
        </template>

        <!-- 操作项 -->
        <template #operations="{ record }">
          <m-button type="link" @click="openDisplayStatusDialog(record)">修改展示状态</m-button>
          <m-button type="link" @click="openBlockStatusDialog(record)">修改屏蔽状态</m-button>
          <m-button type="link" @click="viewDisplayStatusLog(record)">展示状态变更日志</m-button>
          <m-button type="link" @click="viewBlockStatusLog(record)">屏蔽状态变更日志</m-button>
        </template>
      </pm-table>
    </pm-effi>

    <!-- 素材弹窗 -->
    <materials-dialog ref="materialsDialogRef" />

    <!-- 修改展示状态弹窗 -->
    <display-status-dialog ref="displayStatusDialogRef" @success="onRefresh" />

    <!-- 修改屏蔽状态弹窗 -->
    <block-status-dialog ref="blockStatusDialogRef" @success="onRefresh" />

    <!-- 状态变更日志弹窗 -->
    <status-log-dialog ref="statusLogDialogRef" />
  </div>
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less" scoped></style>
