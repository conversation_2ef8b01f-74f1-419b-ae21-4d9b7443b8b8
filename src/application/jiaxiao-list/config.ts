import { TableColumn, ColumnXtype } from 'admin-library';

export const COLUMNS: TableColumn[] = [
  {
    title: '驾校ID',
    dataIndex: 'jiaxiaoId',
    width: 100,
    fixed: 'left'
  },
  {
    title: '驾校简称',
    dataIndex: 'jiaxiaoName',
    oneline: true,
    width: 120,
    fixed: 'left'
  },
  {
    title: '驾校状态',
    dataIndex: 'status',
    width: 120,
    xtype: ColumnXtype.CUSTOM
  },
  {
    title: '驾校信息',
    dataIndex: 'info',
    width: 220,
    xtype: ColumnXtype.CUSTOM
  },
  {
    title: '点评统计',
    dataIndex: 'reviews',
    width: 100,
    xtype: ColumnXtype.CUSTOM
  },
  {
    title: '人员统计',
    dataIndex: 'staff',
    width: 100,
    xtype: ColumnXtype.CUSTOM
  },
  {
    title: '场地统计',
    dataIndex: 'venue',
    width: 100,
    xtype: ColumnXtype.CUSTOM
  },
  {
    title: '素材',
    dataIndex: 'materials',
    width: 100,
    xtype: ColumnXtype.CUSTOM
  }
];
