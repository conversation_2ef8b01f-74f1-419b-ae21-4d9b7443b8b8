/**
 * 驾校列表响应数据
 */
export interface JiaxiaoListResponse {
  /** 主键ID */
  id: number;
  /** 驾校简称 */
  simpleName: string;
  /** 驾校全称 */
  fullName: string;
  /** 统一社会信用代码 */
  uscc: string;
  /** 区县代码 */
  countyCode: string;
  /** 区县名称 */
  countyName: string;
  /** 级别 1-一级 2-二级 3-三级 */
  level: number;
  /** 展示状态 0-隐藏 1-展示 */
  displayStatus: number;
  /** 屏蔽状态 0-正常 1-屏蔽 */
  blockStatus: number;
  /** 入驻状态 0-未入驻 1-已入驻 */
  settleStatus: number;
  /** 基础认证 0-未认证 1-已认证 */
  basicAuth: number;
  /** 点评总数 */
  reviewCount: number;
  /** 平均评分 */
  avgScore: number;
  /** 教练数量 */
  coachCount: number;
  /** 员工数量 */
  staffCount: number;
  /** 训练场数量 */
  trainingCount: number;
  /** 考场数量 */
  examCount: number;
  /** 图片数量 */
  imageCount: number;
  /** 视频数量 */
  videoCount: number;
  /** 创建时间 */
  createTime: number;
  /** 更新时间 */
  updateTime: number;
}

/**
 * 状态变更日志
 */
export interface StatusLogResponse {
  /** 主键ID */
  id: number;
  /** 驾校ID */
  jiaxiaoId: number;
  /** 变更类型 1-展示状态 2-屏蔽状态 */
  changeType: number;
  /** 变更前状态 */
  beforeStatus: number;
  /** 变更后状态 */
  afterStatus: number;
  /** 变更原因 */
  reason: string;
  /** 操作人 */
  operator: string;
  /** 变更时间 */
  changeTime: number;
}

/**
 * 下拉选项
 */
export interface OptionItem {
  label: string;
  value: string | number;
}
