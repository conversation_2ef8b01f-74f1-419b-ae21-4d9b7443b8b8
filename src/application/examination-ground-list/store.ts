import { PaasListResponse } from 'admin-library';
import { Store } from '@simplex/simple-store';
import { ExaminationGroundListStore, ExaminationGroundStatusLogStore } from '@/store/jiaxiao-admin';
import { ExaminationGroundListResponse, StatusLogResponse, OptionItem } from './types';
import { LEVEL_OPTIONS, DISPLAY_STATUS_OPTIONS, BLOCK_STATUS_OPTIONS, AUDIT_STATUS_OPTIONS } from './constants';
import { SOURCE_TYPE_OPTIONS } from '@/utils/constants';
// 考场列表
export const GetExaminationGroundListStore = new ExaminationGroundListStore<
  PaasListResponse<ExaminationGroundListResponse>
>({});

// 考场状态变更日志
export const GetExaminationGroundStatusLogStore = new ExaminationGroundStatusLogStore<
  PaasListResponse<StatusLogResponse>
>({});

// 级别下拉选项
export const LevelListStore = new Store(LEVEL_OPTIONS);

// 展示状态下拉选项
export const DisplayStatusStore = new Store(DISPLAY_STATUS_OPTIONS);

// 来源类型下拉选项
export const SourceTypeStore = new Store(SOURCE_TYPE_OPTIONS);
