import { TableColumn, ColumnXtype } from 'admin-library';
import { DISPLAY_STATUS_OPTIONS } from './constants';

export const COLUMNS: TableColumn[] = [
  {
    title: '考场code',
    dataIndex: 'code',
    width: 100,
    fixed: 'left'
  },
  {
    title: '来源类型',
    dataIndex: 'sourceType',
    width: 100
  },
  {
    title: '来源ID',
    dataIndex: 'sourceId',
    width: 100
  },
  {
    title: '名称',
    dataIndex: 'name',
    oneline: true,
    width: 150,
    fixed: 'left'
  },
  {
    title: '区县',
    dataIndex: 'cityName',
    width: 120
  },
  {
    title: '驾校ID',
    dataIndex: 'jiaxiaoId',
    width: 100
  },
  {
    title: '驾校简称',
    dataIndex: 'jiaxiaoName',
    oneline: true,
    width: 120
  },
  {
    title: '地址',
    dataIndex: 'address',
    width: 190,
    xtype: ColumnXtype.CUSTOM
  },
  {
    title: '展示状态',
    dataIndex: 'showType',
    width: 100,
    render: (value: number) => {
      return value === 1 ? '是' : '否';
    }
    // xtype: ColumnXtype.CUSTOM
  },

  {
    title: '图片',
    dataIndex: 'imageUrl',
    width: 100,
    xtype: ColumnXtype.CUSTOM
  },
  {
    title: '审核状态',
    dataIndex: 'auditStatus',
    width: 100,
    xtype: ColumnXtype.CUSTOM
  }
];
