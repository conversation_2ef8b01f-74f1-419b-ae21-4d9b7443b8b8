import { defineComponent, ref, reactive, toRefs } from 'vue';
import { ModelController } from 'admin-library';
import { MESSAGE_TYPE, MUtils } from '@paas/paas-library';
import { COLUMNS } from './config';
import { GetExaminationGroundListStore, LevelListStore, DisplayStatusStore, SourceTypeStore } from './store';
import AMapComp from '@/components/a-map/index.vue';
import { useAMap } from '@/components/a-map/utils';
import { useCityDataStore } from '@/pinia/city-data';
import MaterialsDialog from '@/components/materials-dialog/index.vue';

export default defineComponent({
  components: {
    AMapComp,
    MaterialsDialog
  },
  setup() {
    const aMapStore = useAMap();

    // 常量
    const constants = {
      COLUMNS
    };

    const state = reactive({});

    // 控制器
    const controller = new ModelController({
      table: {
        store: GetExaminationGroundListStore
      },
      search: {
        level: {
          store: LevelListStore
        },
        showType: {
          store: DisplayStatusStore
        },
        sourceType: {
          store: SourceTypeStore
        }
      }
    });

    controller.table.onRequest.use(params => {
      return params;
    });

    // 组件引用
    const components = {
      materialsDialogRef: ref<InstanceType<typeof MaterialsDialog>>()
    };

    // 方法
    const methods = {
      /**
       * 获取级别标签颜色
       */
      getLevelTagColor(level: number) {
        const colorMap = {
          1: 'green',
          2: 'orange',
          3: 'blue'
        };
        return colorMap[level] || 'default';
      },

      /**
       * 获取审核状态文本
       */
      getAuditStatusText(auditStatus: number) {
        const statusMap = {
          0: '待审核',
          1: '已通过',
          2: '已拒绝'
        };
        return statusMap[auditStatus] || '未知';
      },

      /**
       * 获取审核状态标签颜色
       */
      getAuditStatusTagColor(auditStatus: number) {
        const colorMap = {
          0: 'orange',
          1: 'green',
          2: 'red'
        };
        return colorMap[auditStatus] || 'default';
      },

      /**
       * 处理展示状态切换
       */
      // async handleDisplayStatusChange(record: any, checked: boolean) {
      //   const action = checked ? '展示' : '隐藏';
      //   const currentStatus = record.displayStatus === 1 ? '展示' : '隐藏';

      //   MUtils.confirm({
      //     title: '提示',
      //     content: `确定修改展示状态吗?`,
      //     type: MESSAGE_TYPE.warning
      //   }).then(res => {
      //     console.log(res, '==========');
      //   });
      // },

      /**
       * 查看地址详情
       */
      onMap(record: any) {
        aMapStore.aMapRef.value.open({
          name: record.name,
          longitude: record.longitude,
          latitude: record.latitude
        });
      },

      /**
       * 查看图片详情
       */
      viewImage(imageUrl: string) {
        // TODO: 实现图片详情查看功能
        console.log('查看图片:', imageUrl);
      },

      /**
       * 刷新表格数据
       */
      onRefresh() {
        controller.tableRequest();
      },

      /**
       * 打开素材弹窗
       */
      openMaterialsDialog(record: any) {
        components.materialsDialogRef.value.open(record.id);
      }
    };

    return {
      aMapStore,
      ...toRefs(state),
      ...constants,
      ...components,
      ...methods,
      controller
    };
  }
});
