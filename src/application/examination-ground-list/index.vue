<template>
  <pm-effi :controller="controller">
    <pm-search class="padding-between">
      <pm-search-single
        :antdProps="{
          placeholder: '考场code'
        }"
        data-index="code"
        xtype="INPUT"
      />
      <pm-search-single
        :antdProps="{
          placeholder: '来源类型',
          fieldNames: { label: 'label', value: 'value' }
        }"
        data-index="sourceType"
        xtype="SELECT"
      />
      <pm-search-single
        :antdProps="{
          placeholder: '来源ID'
        }"
        data-index="sourceId"
        xtype="INPUT"
      />
      <pm-search-single
        :antdProps="{
          placeholder: '名称'
        }"
        data-index="name"
        xtype="INPUT"
      />
      <pm-search-single
        :antdProps="{
          placeholder: '驾校ID',
          type: 'number'
        }"
        data-index="jiaxiaoId"
        xtype="INPUT"
      />
      <pm-search-single
        :antdProps="{
          placeholder: '驾校简称'
        }"
        data-index="jiaxiaoName"
        xtype="INPUT"
      />

      <pm-search-single
        :antdProps="{
          placeholder: '展示状态',
          fieldNames: { label: 'label', value: 'value' }
        }"
        data-index="showType"
        xtype="SELECT"
      />
    </pm-search>

    <pm-table
      :columns="COLUMNS"
      :useCustomColumn="true"
      :sortNum="false"
      :operations-width="150"
      :operations-fixed="true"
      :antdProps="{
        pagination: false,
        bordered: true,
        size: 'small'
      }"
    >
      <!-- 地址 -->
      <template #address="{ record }">
        <m-tooltip :title="record.address" placement="top">
          <m-button type="link" class="oneline maxwidth" @click="onMap(record)">
            {{ record.address }}
          </m-button>
        </m-tooltip>
      </template>

      <!-- 展示状态 -->
      <!-- <template #showType="{ record }">
        <m-switch :checked="record.showType === 1" @change="checked => handleDisplayStatusChange(record, checked)" />
      </template> -->

      <!-- 图片 -->
      <template #imageUrl="{ record }">
        <m-button v-if="record.imageUrl" type="link" @click="viewImage(record.imageUrl)">查看图片</m-button>
        <span v-else>-</span>
      </template>

      <!-- 审核状态 -->
      <template #auditStatus="{ record }">
        <m-tag :color="getAuditStatusTagColor(record.auditStatus)">
          {{ getAuditStatusText(record.auditStatus) }}
        </m-tag>
      </template>
    </pm-table>
  </pm-effi>

  <a-map-comp :ref="aMapStore?.aMapRef" />
  <!-- 素材弹窗 -->
  <materials-dialog ref="materialsDialogRef" />
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less" scoped></style>
