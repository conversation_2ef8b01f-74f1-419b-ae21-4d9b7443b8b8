import { defineComponent, ref, reactive, toRefs } from 'vue';
import { ModelController } from 'admin-library';
import { MESSAGE_TYPE, MUtils } from '@paas/paas-library';
import { COLUMNS } from './config';
import {
  GetTrainFieldListStore,
  GetTrainFieldStatusLogStore,
  LevelListStore,
  DisplayStatusStore,
  BlockStatusStore,
  AuditStatusStore,
  SourceTypeStore
} from './store';
import AMapComp from '@/components/a-map/index.vue';
import MaterialsDialog from '@/components/materials-dialog/index.vue';
import { useAMap } from '@/components/a-map/utils';
import { useCityDataStore } from '@/pinia/city-data';
import { ChangeTypeEnum, CHANGE_TYPE_OPTIONS } from '@/utils/constants';

export default defineComponent({
  components: {
    AMapComp,
    MaterialsDialog
  },
  setup() {
    const aMapStore = useAMap();
    const cityDataStore = useCityDataStore();

    // 常量
    const constants = {
      COLUMNS,
      cityDataStore,
      CHANGE_TYPE_OPTIONS,
      ChangeTypeEnum
    };

    const state = reactive({
      areaCode: []
    });

    // 控制器
    const controller = new ModelController({
      table: {
        store: GetTrainFieldListStore
      }
      // search: {
      //   tfCategory: {
      //     store: LevelListStore
      //   },
      //   showStatus: {
      //     store: DisplayStatusStore
      //   },
      //   sourceType: {
      //     store: SourceTypeStore
      //   }
      // }
    });

    controller.table.onRequest.use(params => {
      console.log(state.areaCode, 'state.areaCode');
      params.areaCode = state.areaCode.join(',');
      return params;
    });

    const components = {
      materialsDialogRef: ref<InstanceType<typeof MaterialsDialog>>()
    };

    // 方法
    const methods = {
      /**
       * 获取级别文本
       */
      getLevelText(level: number) {
        const levelMap = {
          1: '一级',
          2: '二级',
          3: '三级'
        };
        return levelMap[level] || '未知';
      },

      /**
       * 获取级别标签颜色
       */
      getLevelTagColor(level: number) {
        const colorMap = {
          1: 'green',
          2: 'orange',
          3: 'blue'
        };
        return colorMap[level] || 'default';
      },

      /**
       * 处理展示状态切换
       */
      // handleDisplayStatusChange(record: any, checked: boolean) {
      //   MUtils.confirm({
      //     title: '修改展示状态',
      //     content: `确定修改展示状态吗? `,
      //     type: MESSAGE_TYPE.warning
      //   }).then(res => {
      //     if (res) {
      //       MUtils.toast('修改成功');
      //       controller.tableRequest();
      //     }
      //   });
      // },

      // handleBlackStatusChange(record: any, checked: boolean) {
      //   MUtils.confirm({
      //     title: '修改黑名单状态',
      //     content: `确定修改黑名单状态吗? `,
      //     type: MESSAGE_TYPE.warning
      //   }).then(res => {
      //     if (res) {
      //       MUtils.toast('修改成功');
      //       controller.tableRequest();
      //     }
      //   });
      // },
      /**
       * 查看地址详情
       */
      onMap(record: any) {
        aMapStore.aMapRef.value.open({
          name: record.name,
          longitude: record.longitude,
          latitude: record.latitude
        });
      },

      /**
       * 查看图片详情
       */
      viewImage(imageUrl: string) {
        // TODO: 实现图片详情查看功能
        console.log('查看图片:', imageUrl);
      },

      /**
       * 刷新表格数据
       */
      onRefresh() {
        controller.tableRequest();
      }
    };

    return {
      aMapStore,
      ...toRefs(state),
      ...constants,
      ...components,
      ...methods,
      controller
    };
  }
});
