/**
 * 训练场列表响应数据
 */
export interface TrainingGroundListResponse {
  /** 主键ID */
  id: number;
  /** 训练场名称 */
  name: string;
  /** 驾校ID */
  jiaxiaoId: number;
  /** 驾校简称 */
  jiaxiaoName: string;
  /** 区县代码 */
  countyCode: string;
  /** 区县名称 */
  countyName: string;
  /** 级别 1-一级 2-二级 3-三级 */
  level: number;
  /** 地址 */
  address: string;
  /** 展示状态 0-隐藏 1-展示 */
  displayStatus: number;
  /** 禁用状态 0-正常 1-禁用 */
  blockStatus: number;
  /** 图片URL */
  imageUrl: string;
  /** 审核状态 0-待审核 1-已通过 2-已拒绝 */
  auditStatus: number;
  /** 创建时间 */
  createTime: number;
  /** 更新时间 */
  updateTime: number;
}

/**
 * 状态变更日志
 */
export interface StatusLogResponse {
  /** 主键ID */
  id: number;
  /** 训练场ID */
  trainingGroundId: number;
  /** 变更类型 1-展示状态 2-禁用状态 */
  changeType: number;
  /** 变更前状态 */
  beforeStatus: number;
  /** 变更后状态 */
  afterStatus: number;
  /** 变更原因 */
  reason: string;
  /** 操作人 */
  operator: string;
  /** 变更时间 */
  changeTime: number;
}

/**
 * 下拉选项
 */
export interface OptionItem {
  label: string;
  value: string | number;
}
