import { TableColumn } from 'admin-library';
import { findText } from '@/utils/utils';
import { AuditStatusEnum, AUDIT_STATUS_OPTIONS, SOURCE_ID_TYPE_OPTIONS, CHANGE_TYPE_OPTIONS } from '@/utils/constants';
export const COLUMNS: TableColumn[] = [
  {
    title: '训练场Id',
    dataIndex: 'id',
    width: 100,
    fixed: 'left'
  },
  // {
  //   title: '训练场名称',
  //   dataIndex: 'tfName',
  //   width: 100,
  //   fixed: 'left'
  // },
  {
    title: '来源类型',
    dataIndex: 'sourceType',
    width: 100,
    render: value => {
      return findText(SOURCE_ID_TYPE_OPTIONS, value);
    }
  },
  {
    title: '来源ID',
    dataIndex: 'sourceId',
    width: 100
  },
  {
    title: '驾校ID',
    dataIndex: 'jiaxiaoId',
    width: 100
  },
  {
    title: '展示状态',
    dataIndex: 'showStatus',
    width: 100,
    render: value => {
      return findText(CHANGE_TYPE_OPTIONS, value);
    }
  },
  {
    title: '审核状态',
    dataIndex: 'auditStatus',
    width: 100,
    render: (value: AuditStatusEnum) => {
      return findText(AUDIT_STATUS_OPTIONS, value);
    }
  }
];
