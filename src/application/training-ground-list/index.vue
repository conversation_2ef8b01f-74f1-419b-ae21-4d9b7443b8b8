<template>
  <pm-effi :controller="controller">
    <pm-search class="padding-between">
      <pm-search-single
        :antdProps="{
          placeholder: '训练场Id',
          type: 'number'
        }"
        data-index="id"
        xtype="INPUT"
      />
      <!-- <pm-search-single
        :antdProps="{
          placeholder: '来源类型',
          fieldNames: { label: 'label', value: 'value' }
        }"
        data-index="sourceType"
        xtype="SELECT"
      />
      <pm-search-single
        :antdProps="{
          placeholder: '来源ID'
        }"
        data-index="sourceId"
        xtype="INPUT"
      />
       <pm-search-single
        :antdProps="{
          placeholder: '训练场名称'
        }"
        data-index="name"
        xtype="INPUT"
      />
   -->

      <pm-search-single
        :antdProps="{
          placeholder: '驾校ID',
          type: 'number'
        }"
        data-index="jiaxiaoId"
        xtype="INPUT"
      />
      <!-- <pm-search-single
        :antdProps="{
          placeholder: '驾校简称'
        }"
        data-index="simpleName"
        xtype="INPUT"
      />
      <pm-search-single :span="4" data-index="areaCode" xtype="CUSTOM">
        <template #custom>
          <m-cascader
            v-model:value="areaCode"
            change-on-select
            :options="cityDataStore.data"
            :allowClear="false"
            placeholder="区县"
          />
        </template>
      </pm-search-single>
      <pm-search-single
        :antdProps="{
          placeholder: '级别',
          fieldNames: { label: 'label', value: 'value' }
        }"
        data-index="tfCategory"
        xtype="SELECT"
      />
      <pm-search-single
        :antdProps="{
          placeholder: '展示状态',
          fieldNames: { label: 'label', value: 'value' }
        }"
        data-index="showStatus"
        xtype="SELECT"
      /> -->
    </pm-search>

    <pm-table
      :columns="COLUMNS"
      :useCustomColumn="true"
      :sortNum="false"
      :operations-width="150"
      :operations-fixed="true"
      :antdProps="{
        pagination: false,
        bordered: true,
        size: 'small'
      }"
    >
      <!-- 级别 -->
      <template #level="{ record }">
        <m-tag :color="getLevelTagColor(record.level)">
          {{ getLevelText(record.level) }}
        </m-tag>
      </template>

      <!-- 地址 -->
      <template #address="{ record }">
        <m-tooltip :title="record.address" placement="top">
          <m-button type="link" class="oneline maxwidth" @click="onMap(record)">
            {{ record.address }}
          </m-button>
        </m-tooltip>
      </template>

      <!-- 展示状态 -->
      <!-- <template #showType="{ record }">
        <m-switch
          :checked="record.showType === ChangeTypeEnum.DISPLAY"
          @change="handleDisplayStatusChange(record, checked)"
        />
      </template> -->

      <!-- 黑名单 -->
      <!-- <template #blackStatus="{ record }">
        <m-switch
          :checked="record.blackStatus === ChangeTypeEnum.BLOCK"
          @change="handleBlackStatusChange(record, checked)"
        />
      </template> -->

      <!-- 图片 -->
      <template #imageUrl="{ record }">
        <m-button v-if="record.imageUrl" type="link" @click="viewImage(record.imageUrl)">查看图片</m-button>
        <span v-else>-</span>
      </template>
    </pm-table>
  </pm-effi>

  <a-map-comp :ref="aMapStore?.aMapRef" />

  <!-- 素材弹窗 -->
  <materials-dialog ref="materialsDialogRef" />
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less" scoped></style>
