import { PaasListResponse } from 'admin-library';
import { Store } from '@simplex/simple-store';
import { TrainFieldStatusLogStore } from '@/store/jiaxiao-admin';
import { TrainFieldShowListStore } from '@/store/mclaren';
import { TrainingGroundListResponse, StatusLogResponse, OptionItem } from './types';
import { LEVEL_OPTIONS } from './constants';
import { SOURCE_ID_TYPE_OPTIONS, AUDIT_STATUS_OPTIONS, CHANGE_TYPE_OPTIONS } from '@/utils/constants';

// 训练场列表
export const GetTrainFieldListStore = new TrainFieldShowListStore<PaasListResponse<TrainingGroundListResponse>>({});

// 训练场状态变更日志
export const GetTrainFieldStatusLogStore = new TrainFieldStatusLogStore<PaasListResponse<StatusLogResponse>>({});

// 级别下拉选项
export const LevelListStore = new Store(LEVEL_OPTIONS);

// 展示状态下拉选项
export const DisplayStatusStore = new Store(CHANGE_TYPE_OPTIONS);

// 来源类型下拉选项
export const SourceTypeStore = new Store(SOURCE_ID_TYPE_OPTIONS);
