import { AgentStore, MethodTypeModel } from 'admin-library';

const JIAXIAO_ADMIN = APP.domain['jiaxiao-admin'];

// 驾校列表
export class JiaxiaoListStore<T> extends AgentStore<T> {
  url = `${JIAXIAO_ADMIN}/api/admin/jiaxiao/list.htm?deleted=false`;
  method: MethodTypeModel = 'GET';
}

// 状态变更日志
export class StatusLogStore<T> extends AgentStore<T> {
  url = `${JIAXIAO_ADMIN}/api/admin/jiaxiao/status-log.htm`;
  method: MethodTypeModel = 'GET';
}

// 训练场列表
export class TrainFieldListStore<T> extends AgentStore<T> {
  url = `${JIAXIAO_ADMIN}/api/admin/train-field/list.htm`;
  method: MethodTypeModel = 'GET';
}

// 训练场状态变更日志
export class TrainFieldStatusLogStore<T> extends AgentStore<T> {
  url = `${JIAXIAO_ADMIN}/api/admin/train-field/status-log.htm`;
  method: MethodTypeModel = 'GET';
}

// 招生点列表
export class RegistrationPointListStore<T> extends AgentStore<T> {
  url = `${JIAXIAO_ADMIN}/api/admin/recruit-site/list.htm`;
  method: MethodTypeModel = 'GET';
}

// 招生点状态变更日志
export class RegistrationPointStatusLogStore<T> extends AgentStore<T> {
  url = `${JIAXIAO_ADMIN}/api/admin/recruit-site/status-log.htm`;
  method: MethodTypeModel = 'GET';
}

// 考场列表
export class ExaminationGroundListStore<T> extends AgentStore<T> {
  url = `${JIAXIAO_ADMIN}api/admin/exam-field/list.htm`;
  method: MethodTypeModel = 'GET';
}

// 考场状态变更日志
export class ExaminationGroundStatusLogStore<T> extends AgentStore<T> {
  url = `${JIAXIAO_ADMIN}/api/admin/examination-ground/status-log.htm`;
  method: MethodTypeModel = 'GET';
}

// 班型列表
export class CourseListStore<T> extends AgentStore<T> {
  url = `${JIAXIAO_ADMIN}/api/admin/jiaxiao-course/list.htm`;
  method: MethodTypeModel = 'GET';
}

// 班型状态变更日志
export class CourseStatusLogStore<T> extends AgentStore<T> {
  url = `${JIAXIAO_ADMIN}/api/admin/course/status-log.htm`;
  method: MethodTypeModel = 'GET';
}

// 更新展示状态
export class UpdateDisplayStatusStore<T> extends AgentStore<T> {
  url = `${JIAXIAO_ADMIN}/api/admin/jiaxiao/update-display-status.htm`;
  method: MethodTypeModel = 'POST';
}

// 更新屏蔽状态
export class UpdateBlockStatusStore<T> extends AgentStore<T> {
  url = `${JIAXIAO_ADMIN}/api/admin/jiaxiao/update-block-status.htm`;
  method: MethodTypeModel = 'POST';
}
