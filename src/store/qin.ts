import { AgentStore, MethodTypeModel } from 'admin-library';

const QIN = APP.domain['qin'];

// 获取枚举值下拉列表
export class CodeListStore<T> extends AgentStore<T> {
  url = `${QIN}/api/admin/code/list.htm`;
  method: MethodTypeModel = 'GET';
}

// 查询驾校非标媒资-按照业务类型分组
export class GroupByBizTypeStore<T> extends AgentStore<T> {
  url = `${QIN}/api/admin/nstd-jiaxiao-media/group-by-biz-type.htm`;
  method: MethodTypeModel = 'GET';
}

/** 审核工单 */
export class AuditWorkOderListStore<T> extends AgentStore<T> {
  url = `${QIN}/api/admin/show-todolist/page.htm`;
  method: MethodTypeModel = 'GET';
}
/** 审核详情 */
export class AuditWorkOderViewStore<T> extends AgentStore<T> {
  url = `${QIN}/api/admin/show-todolist/view.htm`;
  method: MethodTypeModel = 'GET';
}
/** 提交审核 */
export class ProcessAuditWorkOderStore<T> extends AgentStore<T> {
  url = `${QIN}/api/admin/show-todolist/process.htm`;
  method: MethodTypeModel = 'POST';
}
