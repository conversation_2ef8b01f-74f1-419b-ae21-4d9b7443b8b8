import { AgentStore, MethodTypeModel } from 'admin-library';

const JIAXIAO_VIP = APP.domain['jiaxiao-vip'];

// 查询存在四级街道的城市
export class CityClueAreaGetDistinctCityCodeStore<T> extends AgentStore<T> {
  url = `${JIAXIAO_VIP}/api/vip/city-clue-area/get-distinct-city-code.htm`;
  method: MethodTypeModel = 'GET';
}

// 获取街道geo数据
export class CityClueAreaViewListStore<T> extends AgentStore<T> {
  url = `${JIAXIAO_VIP}/api/vip/city-clue-area/view-list.htm`;
  method: MethodTypeModel = 'GET';
}
