import { AgentStore, MethodTypeModel } from 'admin-library';

const MCLARREN = APP.domain['mclaren'];

// 班型展示列表
export class CourseShowListStore<T> extends AgentStore<T> {
  url = `${MCLARREN}/api/admin/course-show/list.htm`;
  method: MethodTypeModel = 'GET';
}

// 训练场列表
export class TrainFieldShowListStore<T> extends AgentStore<T> {
  url = `${MCLARREN}/api/admin/train-field-show/list.htm`;
  method: MethodTypeModel = 'GET';
}

// 获取驾校展示变更原因
export class GetShowRuleConfigStore<T> extends AgentStore<T> {
  url = `${MCLARREN}/api/admin/jiaxiao-show/get-show-rule-config.htm`;
  method: MethodTypeModel = 'GET';
}

// 驾校当前所处于的状态
export class GetShowDataStore<T> extends AgentStore<T> {
  url = `${MCLARREN}/api/admin/jiaxiao-show/get-jiaxiao-show-hide-type.htm`;
  method: MethodTypeModel = 'GET';
}

//  展示状态变更申请
export class ApplyShowStore<T> extends AgentStore<T> {
  url = `${MCLARREN}/api/admin/jiaxiao-show/apply-show.htm`;
  method: MethodTypeModel = 'POST';
}

// 查询展示状态变更列表
export class GetShowChangeLogListStore<T> extends AgentStore<T> {
  url = `${MCLARREN}/api/admin/jiaxiao-show-change-log/list.htm`;
  method: MethodTypeModel = 'GET';
}

// 驾校列表
export class JiaxiaoListStore<T> extends AgentStore<T> {
  url = `${MCLARREN}/api/admin/jiaxiao-show/list.htm`;
  method: MethodTypeModel = 'GET';
}

// 招生点列表
export class RecruitSiteShowListStore<T> extends AgentStore<T> {
  url = `${MCLARREN}/api/admin/recruit-site-show/list.htm`;
  method: MethodTypeModel = 'GET';
}

// 展示策略列表
export class DisplayStrategyListStore<T> extends AgentStore<T> {
  url = `${MCLARREN}/api/admin/show-policy/list.htm`;
  method: MethodTypeModel = 'GET';
}

// 更新展示策略
export class UpdateDisplayStrategyStore<T> extends AgentStore<T> {
  url = `${MCLARREN}/api/admin/show-policy/edit.htm`;
  method: MethodTypeModel = 'POST';
}

// 驾校屏蔽-原因规则配置
export class HideRuleConfigStore<T> extends AgentStore<T> {
  url = `${MCLARREN}/api/admin/jiaxiao-show/get-hide-rule-config.htm`;
  method: MethodTypeModel = 'GET';
}

// 驾校屏蔽-申请屏蔽
export class ApplyHideStore<T> extends AgentStore<T> {
  url = `${MCLARREN}/api/admin/jiaxiao-show/apply-hide.htm`;
  method: MethodTypeModel = 'POST';
}

// 驾校上下架黑白名单列表-列表
export class HideChangeLogListStore<T> extends AgentStore<T> {
  url = `${MCLARREN}/api/admin/jiaxiao-show-bw-list/list.htm`;
  method: MethodTypeModel = 'GET';
}

// 驾校上下架黑白名单列表-新增
export class AddHideChangeLogStore<T> extends AgentStore<T> {
  url = `${MCLARREN}/api/admin/jiaxiao-show-bw-list/create.htm`;
  method: MethodTypeModel = 'POST';
}

// 驾校上下架黑白名单列表-更新失效状态
export class UpdateDisableHideChangeLogStore<T> extends AgentStore<T> {
  url = `${MCLARREN}/api/admin/jiaxiao-show-bw-list/update-disable.htm`;
  method: MethodTypeModel = 'POST';
}

// 驾校展示日志-原因下拉框
export class ChangeReasonListStore<T> extends AgentStore<T> {
  url = `${MCLARREN}/api/admin/jiaxiao-show-change-log/reason-list.htm`;
  method: MethodTypeModel = 'GET';
}
