import { AreaCodeResponse } from '@/utils/query/types';

export interface StateModel {
  // 树状结构省市区
  data: ItemModel[];
  // 接口返回的原始数据列表
  defaultList: DefaultItemModel[];
  // tree simple模式options
  simpleList: SimpleItemModel[];
}

// 城市数据格式（接口返回的格式添加 fullName 和 fullCode）
export interface DefaultItemModel extends AreaCodeResponse {
  // 回显选中的code
  fullName: string;
  fullCode: string[];
}

// 处理后的城市数据格式（树状结构）
export interface ItemModel extends Pick<AreaCodeResponse, 'zhixiashi'> {
  // code
  value: string;
  // 城市名
  label: string;
  children?: ItemModel[];
  isLeaf?: boolean;
  disabled?: boolean;
  disableCheckbox?: boolean;
  fullName?: string;
  county?: boolean;
}

// 用于tree simple mode
export interface SimpleItemModel {
  id: string;
  pId: string;
  key: string;
  value: string;
  label: string;
  // 是否是街道
  isStreet: boolean;
  isLeaf: boolean;
  disabled?: boolean;
  disableCheckbox?: boolean;
  fullName: string;
  fullCode: string[];
}
