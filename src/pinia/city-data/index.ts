import { ItemModel, SimpleItemModel, StateModel } from '@/pinia/city-data/types';
import { findTreeNode, formatAreaCodeList } from '@/pinia/city-data/utils';
import { NAME_MAP } from '@/pinia/store-name';
import { useAreaCodeStore } from '@/utils/query';
import { TreeSelectCityItemModel } from '@/utils/types/common';
import { MUtils } from '@paas/paas-library';
import { defineStore } from 'pinia';
import { computed, reactive, toRefs } from 'vue';

export const useCityDataStore = defineStore(NAME_MAP.cityData, () => {
  const areaCodeStore = useAreaCodeStore();

  const state = reactive<StateModel>({
    // 树状结构省市区
    data: null,
    // 接口返回的原始数据列表
    defaultList: [],
    // tree simple模式options
    simpleList: []
  });

  const computeds = {
    // 禁用区
    disabledCounty: computed(() => {
      const disabledCounty = MUtils.deepClone(state.data) as ItemModel[];

      const setDisabled = (data: ItemModel[]) => {
        data.forEach(item => {
          if (item.county) {
            item.disabled = true;
          }
          if (item.children) {
            setDisabled(item.children);
          }
        });
      };
      setDisabled(disabledCounty);

      return disabledCounty;
    }),
    // 删除第三级
    removeThird: computed(() => {
      const removeThird: ItemModel[] = MUtils.deepClone(state.data) as ItemModel[];

      removeThird.forEach(item => {
        // 直辖市隐藏下级
        if (item.zhixiashi) {
          delete item.children;
        } else if (item.children) {
          item.children.forEach(childItem => {
            if (childItem.children) {
              delete childItem.children;
            }
          });
        }
      });

      return removeThird;
    })
  };

  const methods = {
    // 获取省市区数据并处理为组件所需的格式
    async fetchCityData(): Promise<void> {
      if (!areaCodeStore.isSuccess.value) {
        await areaCodeStore.suspense();
      }

      const { data, simpleList, defaultList } = formatAreaCodeList(areaCodeStore.data.value);

      state.data = data;
      state.simpleList = simpleList;
      state.defaultList = defaultList;
    },
    // 选中的以及上级code列表
    getSelectAndHighCode(codeList: TreeSelectCityItemModel[] = []): string[] {
      if (!(codeList instanceof Array)) {
        return [];
      }

      // 所有选中的code列表
      const allCodeList: string[][] = codeList.map(item => {
        let arr = [];
        state.defaultList.some(option => {
          if (item.value === option.code) {
            arr = option.fullCode;
            return true;
          }
        });

        return arr;
      });

      // 所有选中的code列表 合并去重
      const set: Set<string> = new Set();
      allCodeList.forEach(item => {
        item?.forEach(code => {
          set.add(code);
        });
      });

      return [...set];
    },
    // 传入省区市code，筛选出下级省市区tree
    filterCityData(codeList: string[] = []): ItemModel[] {
      return findTreeNode(
        JSON.parse(JSON.stringify(state.data)) as ItemModel[],
        item => {
          return codeList.includes(item.value);
        },
        true
      );
    },
    // simple类型，传入省区市code和tree，筛选出上下级省市区tree
    // 如果不传tree则取全部省市区数据
    filterSimpleCityData(codeList: string[] = [], allList: SimpleItemModel[] = []): SimpleItemModel[] {
      const list: Set<SimpleItemModel> = new Set();
      const _allList = allList?.length ? allList : state.simpleList;

      codeList.forEach(code => {
        const current = _allList.find(item => item.id === code);
        list.add(current);

        // 查找上级
        const searchPrev = (item: SimpleItemModel) => {
          if (item?.pId) {
            const prev = _allList.find(v => v.id === item.pId);

            if (prev) {
              list.add(prev);
              searchPrev(prev);
            }
          }
        };
        searchPrev(current);

        // 查找下级
        const searchNext = (item: SimpleItemModel) => {
          const nextList = _allList.filter(v => v.pId === item.id);

          if (nextList?.length) {
            nextList.forEach(v => {
              list.add(v);
              searchNext(v);
            });
          }
        };
        searchNext(current);
      });

      return [...list];
    },
    // 获取options里所有的code
    getCityDataTreeCode(options: ItemModel[]) {
      const codeList: Set<string> = new Set();

      const setCode = (nodes: ItemModel[]) => {
        nodes.forEach(node => {
          codeList.add(node.value);

          if (node?.children?.length) {
            setCode(node.children);
          }
        });
      };
      setCode(options);

      return [...codeList];
    },
    // simple类型，往列表添加数据
    addSimpleList(list: SimpleItemModel[]) {
      state.simpleList.push(...list);
    },
    // simple类型，获取列表数据
    getSimpleList() {
      return MUtils.deepClone(state.simpleList);
    },
    // 根据cityCode获取城市名称
    getCityName(cityCode: string) {
      const cityObj = state.defaultList.find(item => item.code === cityCode);
      return {
        fullName: cityObj.fullName,
        fullCode: cityObj.fullCode
      };
    }
  };

  return {
    ...toRefs(state),
    ...computeds,
    ...methods
  };
});
