import { AreaCodeResponse } from '@/utils/query/types';
import { MUtils } from '@paas/paas-library';
import { DefaultItemModel, ItemModel, ItemModel as TreeNode, SimpleItemModel } from './types';

export function findTreeNode(
  tree: TreeNode[],
  predicate: (node: TreeNode) => boolean,
  keepSubTree = false
): TreeNode[] {
  const filterHandler = keepSubTree ? filterWithSubTree : filterWithoutSubTree;

  return filter(tree) ?? [];

  function filter(nodes: TreeNode[] | undefined): TreeNode[] | undefined {
    if (!nodes?.length) {
      return nodes;
    }
    return nodes.filter(filterHandler);
  }

  function filterWithSubTree(it: TreeNode): boolean {
    // 如果符合条件，保留整棵子树，不需要递归进去
    if (predicate(it)) {
      return true;
    }

    // 否则根据子孙节点的情况来决定是否需要保留当前节点（作为路径节点）
    it.children = filter(it.children);
    return !!it.children?.length;
  }

  function filterWithoutSubTree(it: TreeNode): boolean {
    // 先筛选子树，如果子树中没有符合条件的，children 会是 [] 或 undefined
    const children = filter(it.children);
    // 根据当前节点情况和子树筛选结果判断是否保留当前节点
    if (predicate(it) || children?.length) {
      // 如果要保留，children 应该用筛选出来的那个；不保留的话就不 care 子节点了
      it.children = children;
      return true;
    }
    return false;
  }
}

// 格式化接口返回的省市区数据
export function formatAreaCodeList(list: AreaCodeResponse[]): {
  data: ItemModel[];
  simpleList: SimpleItemModel[];
  defaultList: DefaultItemModel[];
} {
  const defaultList = MUtils.deepClone(list) as DefaultItemModel[];

  const provinceList: ItemModel[] = [];

  (function () {
    const cityMap: Record<string, ItemModel[]> = {};
    const countyMap: Record<string, ItemModel[]> = {};

    // 将区转化为map
    list.forEach(item => {
      if (item.county) {
        if (!countyMap[item.parentCode]) {
          countyMap[item.parentCode] = [];
        }
        const treeItem: ItemModel = {
          value: item.code,
          label: item.name,
          zhixiashi: item.zhixiashi,
          isLeaf: true,
          county: item.county
        };
        countyMap[item.parentCode].push(treeItem);
      }
    });

    // 将省筛选出来，将区放入市，然后将市转化为map
    list.forEach(item => {
      const treeItem: ItemModel = {
        value: item.code,
        label: item.name,
        zhixiashi: item.zhixiashi
      };

      for (const k in countyMap) {
        if (item.code === k) {
          treeItem.children = countyMap[k];
        }
      }

      if (item.province) {
        provinceList.push(treeItem);
      }
      if (item.city) {
        if (!cityMap[item.parentCode]) {
          cityMap[item.parentCode] = [];
        }
        cityMap[item.parentCode].push(treeItem);
      }
    });
    // 将市放入省
    provinceList.forEach(item => {
      for (const k in cityMap) {
        if (item.value === k) {
          if (item.children && item.children instanceof Array) {
            item.children.push(...cityMap[k]);
          } else {
            item.children = cityMap[k];
          }
        }
      }
    });

    const setFullInfo = (list: ItemModel[], prev?: DefaultItemModel) => {
      list.forEach(item => {
        const current = defaultList.find(defaultItem => defaultItem.code === item.value);
        if (prev) {
          current.fullName = `${prev.fullName}/${item.label}`;
          item.fullName = `${prev.fullName}/${item.label}`;
          current.fullCode = [...prev.fullCode, item.value];
        } else {
          current.fullName = item.label;
          item.fullName = item.label;
          current.fullCode = [item.value];
        }
        if (item.children?.length) {
          setFullInfo(item.children, current);
        }
      });
    };
    setFullInfo(provinceList);
  })();

  const simpleList: SimpleItemModel[] = defaultList.map(item => {
    return {
      id: item.code,
      pId: item.parentCode,
      key: item.code,
      value: item.code,
      label: item.name,
      isStreet: false,
      isLeaf: false,
      fullName: item.fullName,
      fullCode: item.fullCode
    };
  });

  return {
    simpleList,
    defaultList,
    data: provinceList
  };
}
