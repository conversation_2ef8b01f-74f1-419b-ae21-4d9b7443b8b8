import { TypeEnum } from '@/pinia/open-preview-img/constants';
import { StateModel } from '@/pinia/open-preview-img/types';
import { NAME_MAP } from '@/pinia/store-name';
import { IframeScreen } from '@paas/paas-library/src/initialize/dialog-utils';
import { MESSAGE_TYPE, MUtils } from 'admin-library';
import { defineStore } from 'pinia';
import { reactive, toRefs } from 'vue';

export const useOpenPreviewImgStore = defineStore(NAME_MAP.openPreviewImg, () => {
  const state = reactive<StateModel>({
    visible: false,
    type: TypeEnum.IMG,
    url: [],
    current: 0,
    text: ''
  });

  const computeds = {};

  const methods = {
    open({ url, current = 0, type = TypeEnum.IMG, text = '' }: Omit<StateModel, 'visible'>): void {
      if (!url) {
        MUtils.toast('暂无图片', MESSAGE_TYPE.warning);
        return;
      }

      IframeScreen(true, () => {
        state.visible = true;
        state.type = type;
        state.text = text;
        if (typeof url === 'string') {
          state.url = [url];
        } else if (url instanceof Array) {
          state.url = url as string[];
          state.current = current;
        }
      });
    },
    onChange(value: boolean) {
      if (!value) {
        IframeScreen(false, () => {
          state.visible = false;
        });
      }
    }
  };

  return {
    ...toRefs(state),
    ...computeds,
    ...methods
  };
});
