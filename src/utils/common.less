.margin-right-10 {
  margin-right: 10px !important;
}
.margin-bottom-20 {
  margin-bottom: 20px !important;
}
.oneline {
  overflow: hidden; /* 隐藏超出的内容 */
  text-overflow: ellipsis; /* 当内容被裁剪时显示省略标记(...) */
  white-space: nowrap; /* 防止文字换行 */
}
.del {
  color: rgb(245, 26, 26);
}
.del:hover {
  color: rgb(245, 68, 68);
}
.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.flex-end {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}
.flex-10 {
  display: flex;
  gap: 10px;
  align-items: center;
}
.flex-30 {
  display: flex;
  gap: 30px;
  align-items: center;
}
.width-150 {
  width: 150px !important;
}
.width-200 {
  width: 200px !important;
}
.mark-red {
  color: red;
}
.ant-picker {
  width: 100%;
}
.padding-20 {
  padding: 20px;
}
.nowrap {
  white-space: nowrap;
}
.tips {
  margin: 5px;
  font-size: 13px;
  color: gray;
}
