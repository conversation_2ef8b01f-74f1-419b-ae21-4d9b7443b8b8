import { AgentStore } from '@paas/paas-library/src/store/agent';
import * as CryptoJS from 'crypto-js';

type ResponseModel<D> =
  | {
      success: true;
      data: D;
      // eslint-disable-next-line no-mixed-spaces-and-tabs
    }
  | {
      success: false;
      error: any;
      // eslint-disable-next-line no-mixed-spaces-and-tabs
    };

type Callback<R> = (res: R) => unknown;

type QueueModel = Record<
  string,
  {
    callbacks: Callback<ResponseModel<any>>[];
    promise: Promise<void>;
  }
>;

class Md5Encryptor {
  static encrypt(value: any): string {
    return CryptoJS.MD5(value).toString();
  }
}

class RequestQueue {
  private readonly queue: QueueModel;

  constructor() {
    this.queue = {};
  }

  addRequest<D>(store: AgentStore<D>, params: Record<string, any>, callback: Callback<ResponseModel<D>>) {
    const key = Md5Encryptor.encrypt(store) + Md5Encryptor.encrypt(JSON.stringify(params));

    if (this.queue[key]) {
      // 如果队列中已有相同请求，则直接使用之前的请求结果
      this.queue[key].callbacks.push(callback);
      return;
    }

    this.queue[key] = {
      callbacks: [callback],
      promise: store
        .request(params)
        .getData()
        .then((data: D) => {
          this.queue[key].callbacks.forEach(cb =>
            cb({
              success: true,
              data
            })
          );
        })
        .catch(err => {
          this.queue[key].callbacks.forEach(cb =>
            cb({
              success: false,
              error: err
            })
          );
        })
        .finally(() => {
          delete this.queue[key];
        })
    };
  }
}

export default RequestQueue;
