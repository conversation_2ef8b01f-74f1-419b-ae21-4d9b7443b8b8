export enum InfoTypeEnum {
  // 驾校训练场
  TRAINING = 'JX_XLC',
  // 驾校招生点
  REGISTRATION = 'JX_BMD'
}

export enum SourceTypeEnum {
  // 平台数据
  PLATFORM = 1,
  // 用户数据-paas
  USER = 2
}
export const SOURCE_TYPE_OPTIONS = [
  {
    label: '平台数据',
    value: SourceTypeEnum.PLATFORM
  },
  {
    label: '用户数据-paas',
    value: SourceTypeEnum.USER
  }
];

// 来源类型
export enum SourceIdTypeEnum {
  RLSD = 1,
  NSTD_PAAS = 10
}

export const SOURCE_ID_TYPE_OPTIONS = [
  { value: SourceIdTypeEnum.RLSD, label: '标准数据' },
  { value: SourceIdTypeEnum.NSTD_PAAS, label: 'paas非标数据' }
];

export const APP_SPACE_ID = {
  // c端
  consumer: 'ed60b981ed6081cf17ca'
};

const enum AuditStatusEnum {
  /** 未知 */
  UNKNOWN = -1,
  /** 待审核 */
  WAIT_AUDIT = 0,
  /** 审核中 */
  AUDITING = 1,
  /** 审核不通过 */
  AUDIT_FAIL = 2,
  /** 审核通过 */
  AUDIT_PASS = 3
}

// 审核状态选项
export const AUDIT_STATUS_OPTIONS = [
  { value: AuditStatusEnum.UNKNOWN, label: '未知' },
  { value: AuditStatusEnum.WAIT_AUDIT, label: '待审核' },
  { value: AuditStatusEnum.AUDITING, label: '审核中' },
  { value: AuditStatusEnum.AUDIT_FAIL, label: '审核不通过' },
  { value: AuditStatusEnum.AUDIT_PASS, label: '审核通过' }
];

// 状态变更类型
export enum ChangeTypeEnum {
  DISPLAY = 0,
  BLOCK = 1
}

// 状态变更类型选项
export const CHANGE_TYPE_OPTIONS = [
  { value: ChangeTypeEnum.DISPLAY, label: '隐藏' },
  { value: ChangeTypeEnum.BLOCK, label: '显示' }
];
