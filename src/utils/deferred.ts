export interface DeferredModel<T> {
  promise: Promise<T>;
  resolve: (value: T | PromiseLike<T>) => void;
  reject: (reason: any) => void;
}

const Deferred = function <T>(): DeferredModel<T> {
  const dfd: Partial<DeferredModel<T>> = {};

  dfd.promise = new Promise((resolve, reject) => {
    dfd.resolve = resolve;
    dfd.reject = reject;
  });

  return dfd as DeferredModel<T>;
};

export default Deferred;
