import { CityClueAreaGetDistinctCityCodeStore } from '@/store/jiaxiao-vip';
import { CityDataIncludeResponse, AreaCodeResponse } from '@/utils/query/types';

// 包含四级区划的citycode
export const GetCityDataIncludeStreetStore = new CityClueAreaGetDistinctCityCodeStore<CityDataIncludeResponse[]>({});

// 省市区
export const GetAreaCodeStore = function (): Promise<AreaCodeResponse[]> {
  return new Promise((resolve, reject) => {
    const url = 'https://web-resource.mucang.cn/public-data/city/area.code.json';
    // eslint-disable-next-line no-restricted-syntax
    const request = new XMLHttpRequest();
    request.open('get', url);
    request.send(null);
    request.onload = function () {
      if (request.status === 200) {
        resolve(JSON.parse(request.responseText) as AreaCodeResponse[]);
      } else {
        reject();
      }
    };
  });
};
