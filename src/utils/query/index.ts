import { AREA_CODE, CITY_DATA_INCLUDE } from '@/utils/query/key';
import { GetAreaCodeStore, GetCityDataIncludeStreetStore } from '@/utils/query/store';
import { useQuery } from '@tanstack/vue-query';

// 包含四级区划的citycode
export const useCityDataIncludeQuery = () =>
  useQuery({
    queryKey: [CITY_DATA_INCLUDE],
    queryFn: () => GetCityDataIncludeStreetStore.request().getData(),
    refetchOnWindowFocus: false,
    retry: false
  });

// 省市区
export const useAreaCodeStore = () =>
  useQuery({
    queryKey: [AREA_CODE],
    queryFn: GetAreaCodeStore,
    refetchOnWindowFocus: false,
    retry: false
  });
