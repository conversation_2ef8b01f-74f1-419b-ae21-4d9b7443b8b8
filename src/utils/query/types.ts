import { AreaCodeTypeEnum } from '@/utils/query/types';

export interface CityDataIncludeResponse {
  cityClueArea: string;
  cityCode: string;
  cityName: string;
}

// 接口返回的城市数据格式
export interface AreaCodeResponse {
  city: boolean;
  code: string;
  county: boolean;
  name: string;
  parentCode: string;
  pinyin: string;
  province: boolean;
  shengzhixia: boolean;
  simpleName: string;
  type: AreaCodeTypeEnum;
  zhixiashi: boolean;
}
