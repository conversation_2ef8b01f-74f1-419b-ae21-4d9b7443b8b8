.tag-com {
  color: #fff;
  backdrop-filter: blur(4px);
  z-index: 1;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 5px;
  font-size: 12px;
  font-weight: 500;
  position: absolute;
  padding: 2px 6px;
  top: 3px;
  right: 3px;
}

.material-image {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
  cursor: pointer;
  transition: transform 0.2s;

  &:hover {
    transform: scale(1.05);
  }
}

.image-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100px;
  height: 100px;
  margin: 10px 0px;
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  .tag {
    .tag-com();
  }
}

// 视频容器样式
.video-container {
  position: relative;
  width: 100px;
  height: 100px;
  margin: 10px 0px;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.2s;

  &:hover {
    transform: scale(1.05);

    .video-overlay {
      opacity: 1;
    }
  }

  .video-preview {
    width: 100%;
    height: 100%;
    object-fit: cover;
    background-color: #000;
  }

  .video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0.8;
    transition: opacity 0.2s;

    .play-icon {
      color: white;
      font-size: 24px;
      font-weight: bold;
      text-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
    }
  }
  .tag {
    .tag-com();
  }
}

// 多素材弹框样式
.materials-dialog {
  :deep(.ant-modal) {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
  }

  :deep(.ant-modal-content) {
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  :deep(.ant-modal-body) {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    display: flex;
    justify-content: center;
  }
}

.materials-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  padding: 20px 0;
  width: 100%;
}

.material-item {
  width: 500px;
  height: 500px;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }

  .image-item {
    position: relative;
    width: 500px;
    height: 500px;

    .material-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .video-item {
    width: 500px;
    height: 500px;
    position: relative;

    .material-video {
      width: 100%;
      height: 100%;
      object-fit: cover;
      background-color: #000;
    }

    .video-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.3);
      display: flex;
      justify-content: center;
      align-items: center;
      transition: background-color 0.2s;

      .play-icon {
        color: white;
        font-size: 32px;
        font-weight: bold;
        text-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
      }
    }

    &:hover .video-overlay {
      background-color: rgba(0, 0, 0, 0.5);
    }
  }

  .material-tag {
    position: absolute;
    top: 8px;
    right: 8px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    backdrop-filter: blur(4px);
    z-index: 1;
  }
}
