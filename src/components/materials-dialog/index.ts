import { defineComponent, ref, reactive, toRefs, computed } from 'vue';
import { MESSAGE_TYPE, <PERSON><PERSON><PERSON><PERSON>, ModelController } from 'admin-library';
import { useOpenPreviewImgStore } from '@/pinia/open-preview-img';
import { TypeEnum } from '@/pinia/open-preview-img/constants';
import { COLUMNS } from './config';
import { GetGroupByBizTypeStore } from './store';
import { typeMap, MaterialTypeEnum } from './constants';

export default defineComponent({
  setup() {
    // 状态
    const state = reactive({
      visible: false,
      currentJiaxiaoId: null,
      tableData: [],
      loading: false,
      materialsDialogVisible: false,
      currentMaterialsList: [],
      currentMaterialType: ''
    });

    // 常量
    const constants = {
      COLUMNS,
      typeMap,
      MaterialTypeEnum
    };

    const controller = new ModelController({
      table: {
        store: GetGroupByBizTypeStore
      }
    });

    controller.table.onResponse.use(responseData => {
      return responseData;
    });

    controller.table.onRequest.use(params => {
      params.jiaxiaoId = state.currentJiaxiaoId;
      return params;
    });

    // 预览组件store
    const openPreviewImgStore = useOpenPreviewImgStore();

    // 计算属性
    const materialsDialogTitle = computed(() => {
      if (state.currentMaterialType === 'image') {
        return `图片素材 (共${state.currentMaterialsList.length}张)`;
      } else if (state.currentMaterialType === 'video') {
        return `视频素材 (共${state.currentMaterialsList.length}个)`;
      }
      return '素材预览';
    });

    const methods = {
      /**
       * 打开弹框
       */
      async open(jiaxiaoId: number) {
        state.visible = true;
        state.currentJiaxiaoId = jiaxiaoId;
        controller.tableRequest();
      },

      /**
       * 关闭弹框
       */
      handleCancel() {
        state.visible = false;
        state.currentJiaxiaoId = null;
        state.tableData = [];
      },

      /**
       * 预览图片
       */
      previewImage(imageUrl: string) {
        if (!imageUrl) {
          MUtils.toast('图片地址无效', MESSAGE_TYPE.error);
          return;
        }

        openPreviewImgStore.open({
          url: imageUrl,
          type: TypeEnum.IMG,
          current: 0
        });
      },

      /**
       * 预览视频
       */
      previewVideo(videoUrl: string) {
        if (!videoUrl) {
          MUtils.toast('视频地址无效', MESSAGE_TYPE.error);
          return;
        }

        openPreviewImgStore.open({
          url: videoUrl,
          type: TypeEnum.VIDEO
        });
      },

      /**
       * 打开多素材展示弹框
       */
      openMaterialsDialog(materialsList: any[], materialType: string) {
        console.log('materialsList', materialsList);
        if (!materialsList || materialsList.length === 0) {
          MUtils.toast('素材列表为空', MESSAGE_TYPE.error);
          return;
        }

        state.currentMaterialsList = materialsList;
        state.currentMaterialType = materialType;
        state.materialsDialogVisible = true;
      },

      /**
       * 关闭多素材展示弹框
       */
      closeMaterialsDialog() {
        state.materialsDialogVisible = false;
        state.currentMaterialsList = [];
        state.currentMaterialType = '';
      },

      /**
       * 预览素材（从多素材弹框中点击）
       */
      previewMaterial(materialUrl: string, materialType: string) {
        if (!materialUrl) {
          MUtils.toast('素材地址无效', MESSAGE_TYPE.error);
          return;
        }

        if (materialType === 'image') {
          methods.previewImage(materialUrl);
        } else if (materialType === 'video') {
          methods.previewVideo(materialUrl);
        }
      }
    };

    return {
      ...toRefs(state),
      ...constants,
      ...methods,
      controller,
      materialsDialogTitle
    };
  }
});
