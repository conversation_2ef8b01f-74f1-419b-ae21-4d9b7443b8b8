/**
 * 素材数据模型
 */
export interface MaterialsModel {
  /** 主键ID */
  id: number;
  /** 驾校ID */
  jiaxiaoId: number;
  /** 素材名称 */
  name: string;
  /** 素材类型 1-图片 2-视频 3-文档 */
  type: number;
  /** 图片URL */
  imageUrl: string;
  /** 文件URL */
  fileUrl: string;
  /** 文件大小 */
  fileSize: number;
  /** 创建时间 */
  createTime: string;
  /** 更新时间 */
  updateTime: string;
}

/**
 * 素材列表响应数据
 */
export interface MaterialsListResponse {
  /** 素材列表 */
  itemList: MaterialsModel[];
  /** 总数 */
  total: number;
}
