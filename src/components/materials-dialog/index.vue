<template>
  <pm-dialog v-model:visible="visible" title="用户素材" width="600px" :footer="null" @cancel="handleCancel">
    <pm-effi :controller="controller">
      <pm-table
        :columns="COLUMNS"
        :loading="loading"
        :pagination="false"
        :bordered="true"
        :sort-num="false"
        size="small"
      >
        <template #mediaBizType="{ record }">
          {{ record.mediaBizType.desc }}
        </template>

        <template #medias="{ record }">
          <div v-if="record.medias.length > 1">
            <a
              v-if="
                record.mediaBizType.code === MaterialTypeEnum.LOGO ||
                record.mediaBizType.code === MaterialTypeEnum.COVER ||
                record.mediaBizType.code === MaterialTypeEnum.IMAGE_MATERIAL
              "
              href="javascript:void(0)"
              @click="openMaterialsDialog(record.medias, 'image')"
            >
              共{{ record.medias.length }}张图片
            </a>
            <a
              v-if="
                record.mediaBizType.code === MaterialTypeEnum.VIDEO_COVER ||
                record.mediaBizType.code === MaterialTypeEnum.VIDEO_MATERIAL
              "
              href="javascript:void(0)"
              @click="openMaterialsDialog(record.medias, 'video')"
            >
              共{{ record.medias.length }}个视频
            </a>
          </div>
          <div v-else>
            <div
              class="image-container"
              v-if="
                record.mediaBizType.code === MaterialTypeEnum.LOGO ||
                record.mediaBizType.code === MaterialTypeEnum.COVER ||
                record.mediaBizType.code === MaterialTypeEnum.IMAGE_MATERIAL
              "
            >
              <img
                :src="record.medias[0].media.media?.url"
                @click="previewImage(record.medias[0].media.media?.url)"
                class="image-preview"
              />
              <div class="tag" v-if="record.mediaBizType.code === MaterialTypeEnum.IMAGE_MATERIAL">
                {{ record.medias[0].mediaLabel.desc }}
              </div>
            </div>
            <div
              v-if="
                record.mediaBizType.code === MaterialTypeEnum.VIDEO_COVER ||
                record.mediaBizType.code === MaterialTypeEnum.VIDEO_MATERIAL
              "
              class="video-container"
            >
              <video
                :src="record.medias[0].media.media.url"
                class="video-preview"
                preload="metadata"
                :poster="record.medias[0].media.cover?.url"
              ></video>
              <div class="video-overlay" @click="previewVideo(record.medias[0].media.media?.url)">
                <div class="play-icon">▶</div>
              </div>
              <div class="tag" v-if="record.mediaBizType.code === MaterialTypeEnum.VIDEO_MATERIAL">
                {{ record.medias[0].mediaLabel.desc }}
              </div>
            </div>

            <div v-if="record.mediaBizType.code === MaterialTypeEnum.VR_MATERIAL">
              <a :href="record.medias[0].media.cover.url" target="_blank">{{ record.medias[0].media.cover.url }}</a>
            </div>
          </div>
        </template>
      </pm-table>
    </pm-effi>

    <!-- 多素材展示弹框 -->
    <pm-dialog
      v-model:visible="materialsDialogVisible"
      :title="materialsDialogTitle"
      width="90vw"
      :footer="null"
      :mask-closable="true"
      class="materials-dialog"
      @cancel="closeMaterialsDialog"
    >
      <div class="materials-grid">
        <div
          v-for="(item, index) in currentMaterialsList"
          :key="index"
          class="material-item"
          @click="previewMaterial(item.media.media?.url, currentMaterialType)"
        >
          <!-- 图片素材 -->
          <div v-if="currentMaterialType === 'image'" class="image-item">
            <img :src="item.media.media?.url" class="material-image" />
            <div class="material-tag">{{ item.mediaLabel.desc }}</div>
          </div>

          <!-- 视频素材 -->
          <div v-else-if="currentMaterialType === 'video'" class="video-item">
            <video :src="item.media.media.url" class="material-video" preload="metadata"></video>
            <div class="video-overlay">
              <div class="play-icon">▶</div>
            </div>
            <div class="material-tag">{{ item.mediaLabel.desc }}</div>
          </div>
        </div>
      </div>
    </pm-dialog>
  </pm-dialog>
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less" scoped></style>
