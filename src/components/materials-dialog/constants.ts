export enum MaterialTypeEnum {
  // logo
  LOGO = 10,
  // 封面
  COVER = 20,
  // 视频封面
  VIDEO_COVER = 30,
  // 图片素材
  IMAGE_MATERIAL = 40,
  // 视频素材
  VIDEO_MATERIAL = 50,
  // VR素材
  VR_MATERIAL = 60
}

export const typeMap = {
  [MaterialTypeEnum.LOGO]: 'logo',
  [MaterialTypeEnum.COVER]: '封面',
  [MaterialTypeEnum.VIDEO_COVER]: '视频封面',
  [MaterialTypeEnum.IMAGE_MATERIAL]: '图片素材',
  [MaterialTypeEnum.VIDEO_MATERIAL]: '视频素材',
  [MaterialTypeEnum.VR_MATERIAL]: 'VR素材'
};
