import { StateModel } from '@/pinia/open-preview-img/types';
import { computed, nextTick, onUnmounted, ref, watch } from 'vue';

// 常量配置
const CONSTANTS = {
  SELECTORS: {
    PREVIEW: '.ant-image-preview',
    PREVIEW_IMG: '.ant-image-preview-img',
    LEFT_ARROW: '.ant-image-preview-switch-left',
    RIGHT_ARROW: '.ant-image-preview-switch-right'
  }
} as const;

// 当前版本 antd 没有返回图片预览的翻页事件
// 手动监听翻页按钮提供页码
export const usePageInfoStore = (openPreviewImgStore: StateModel) => {
  const current = ref<number>(null);
  let observer: MutationObserver | null = null;
  let leftArrowListenerAdded = false;
  let rightArrowListenerAdded = false;

  // 查找可见图片的索引
  const findVisibleImageIndex = (images: NodeListOf<Element>): number => {
    for (let i = 0; i < images.length; i++) {
      const imgElement = images[i] as HTMLElement;
      const style = window.getComputedStyle(imgElement);

      if (style.display !== 'none' && imgElement.offsetParent !== null) {
        const imgSrc = (imgElement as HTMLImageElement).src;
        const urlIndex = openPreviewImgStore.url.findIndex(url => url && imgSrc.includes(url));

        if (urlIndex !== -1) {
          console.log(`箭头点击: 匹配到 src ${imgSrc}，索引为 ${urlIndex}`);
          return urlIndex;
        }

        console.warn(`箭头点击: 无法匹配 src ${imgSrc}，回退到 DOM 索引 ${i}`);
        return i;
      }
    }
    return -1;
  };

  // 处理箭头点击
  const handleArrowClick = (e: MouseEvent) => {
    const target = e.currentTarget as HTMLElement;
    const isLeft = target.classList.contains(CONSTANTS.SELECTORS.LEFT_ARROW.slice(1));
    const isRight = target.classList.contains(CONSTANTS.SELECTORS.RIGHT_ARROW.slice(1));

    if (!isLeft && !isRight) {
      return;
    }
    nextTick(() => {
      const images = document.querySelectorAll(CONSTANTS.SELECTORS.PREVIEW_IMG);
      if (!images.length) {
        return;
      }

      const foundIndex = findVisibleImageIndex(images);
      if (foundIndex !== -1 && foundIndex !== current.value) {
        current.value = foundIndex;
        console.log(`箭头点击: 更新索引为 ${foundIndex}`);
      }
    });
  };

  // 清理监听器和观察器
  const cleanupListeners = () => {
    console.log('cleanupListeners');
    const previewContainer = document.querySelector(CONSTANTS.SELECTORS.PREVIEW);
    if (!previewContainer) {
      return;
    }

    const removeListener = (selector: string, flag: boolean) => {
      const element = previewContainer.querySelector(selector);
      if (element && flag) {
        element.removeEventListener('click', handleArrowClick);
        return true;
      }
      return false;
    };

    removeListener(CONSTANTS.SELECTORS.LEFT_ARROW, leftArrowListenerAdded);
    removeListener(CONSTANTS.SELECTORS.RIGHT_ARROW, rightArrowListenerAdded);
    leftArrowListenerAdded = false;
    rightArrowListenerAdded = false;

    if (observer) {
      observer.disconnect();
      observer = null;
    }
    console.log('cleanupListeners end: ', observer);
  };

  const findPreviewContainer = (mutations: MutationRecord[]): HTMLElement | null => {
    for (const mutation of mutations) {
      for (const node of Array.from(mutation.addedNodes)) {
        if (node.nodeType === Node.ELEMENT_NODE) {
          const element = node as HTMLElement;
          if (element.classList.contains(CONSTANTS.SELECTORS.PREVIEW.slice(1))) {
            return element;
          }
          const container = element.querySelector(CONSTANTS.SELECTORS.PREVIEW);
          if (container) {
            return container as HTMLElement;
          }
        }
      }
    }
    return null;
  };

  // 建立预览监听
  const setupPreviewListeners = () => {
    console.log('setupPreviewListeners');
    observer = new MutationObserver(mutations => {
      const previewContainer = document.querySelector(CONSTANTS.SELECTORS.PREVIEW) || findPreviewContainer(mutations);

      if (!previewContainer || (leftArrowListenerAdded && rightArrowListenerAdded)) {
        return;
      }

      const setupArrowListener = (selector: string, isLeft: boolean) => {
        const arrow = previewContainer.querySelector(selector);
        if (arrow && (isLeft ? !leftArrowListenerAdded : !rightArrowListenerAdded)) {
          arrow.addEventListener('click', handleArrowClick);
          return true;
        }
        return false;
      };

      leftArrowListenerAdded = setupArrowListener(CONSTANTS.SELECTORS.LEFT_ARROW, true);
      rightArrowListenerAdded = setupArrowListener(CONSTANTS.SELECTORS.RIGHT_ARROW, false);

      if (leftArrowListenerAdded && rightArrowListenerAdded) {
        observer?.disconnect();
        observer = null;
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true
    });
  };

  watch(
    () => openPreviewImgStore.visible,
    isVisible => {
      if (isVisible) {
        current.value = openPreviewImgStore.current;

        // 只有一张图不需要监听
        if (openPreviewImgStore.url.length <= 1) {
          return;
        }

        leftArrowListenerAdded = false;
        rightArrowListenerAdded = false;
        nextTick(setupPreviewListeners);
      } else {
        cleanupListeners();
      }
    },
    { immediate: false }
  );

  onUnmounted(cleanupListeners);

  return {
    pageInfo: computed(() => {
      if (!openPreviewImgStore.url?.length) {
        return '-';
      }
      return `${current.value + 1} / ${openPreviewImgStore.url.length}`;
    })
  };
};
