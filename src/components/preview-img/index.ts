import { useOpenPreviewImgStore } from '@/pinia/open-preview-img';
import { TypeEnum } from '@/pinia/open-preview-img/constants';
import { CloseCircleOutlined } from '@ant-design/icons-vue';
import { defineComponent, ref } from 'vue';
import { usePageInfoStore } from './utils';

export default defineComponent({
  components: {
    CloseCircleOutlined
  },
  setup() {
    const openPreviewImgStore = useOpenPreviewImgStore();
    const pageInfoStore = usePageInfoStore(openPreviewImgStore);

    const components = {
      videoRef: ref<HTMLVideoElement>(null)
    };

    const constants = {
      TypeEnum
    };

    const methods = {
      onCloseVideo() {
        components.videoRef.value.pause();
        openPreviewImgStore.onChange(false);
      },
      test() {
        console.log(27);
      }
    };

    return {
      openPreviewImgStore,
      pageInfoStore,
      ...components,
      ...constants,
      ...methods
    };
  }
});
