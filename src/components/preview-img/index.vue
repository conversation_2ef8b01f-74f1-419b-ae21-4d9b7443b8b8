<template>
  <div v-if="openPreviewImgStore.visible">
    <template v-if="openPreviewImgStore.type === TypeEnum.IMG">
      <div style="display: none">
        <m-image-preview-group
          :preview="{
            visible: openPreviewImgStore.visible,
            onVisibleChange: openPreviewImgStore.onChange,
            current: openPreviewImgStore.current
          }"
        >
          <m-image v-for="(url, index) in openPreviewImgStore.url" :key="index" :src="url" />
        </m-image-preview-group>
      </div>
    </template>
    <teleport to="body" v-else-if="openPreviewImgStore.type === TypeEnum.VIDEO">
      <div class="video-mask">
        <close-circle-outlined @click="onCloseVideo" />
        <video ref="videoRef" :src="openPreviewImgStore.url[0]" controls="controls" autoplay="autoplay" class="video" />
      </div>
    </teleport>

    <div class="page-info">{{ pageInfoStore.pageInfo }}</div>

    <teleport to="body" v-if="openPreviewImgStore.text">
      <div class="body-dialog-text-container" v-html="openPreviewImgStore.text"></div>
    </teleport>
  </div>
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less"></style>
