<template>
  <pm-dialog
    v-model:visible="visible"
    title="地图"
    width="800px"
    :z-index="1010"
    :footer="showFooter ? undefined : null"
    centered
    @close="close"
  >
    <div class="a-map" ref="amapCompRef">
      <div class="btn-list" v-if="isChangeMapType">
        <m-button class="btn-width" v-if="mapType === MapTypeEnum.Map_2D" type="primary" @click="onChangeType">
          2D
        </m-button>
        <m-button class="btn-width" v-if="mapType === MapTypeEnum.Map_Satellite" type="primary" @click="onChangeType">
          卫星
        </m-button>
      </div>
    </div>
    <template #footer>
      <m-button class="btn-width" @click="close">关闭</m-button>
      <m-button class="btn-width" v-if="isMerge" type="primary" @click="onMerge">合并</m-button>
    </template>
  </pm-dialog>
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less" scoped></style>
