import { ICON_TYPE } from '@/components/a-map/constants';
import AMapComp from '@/components/a-map/index.vue';
import { ref } from 'vue';

export const useAMap = () => {
  const components = {
    aMapRef: ref<typeof AMapComp>(null)
  };

  return {
    ...components
  };
};

export const getIcon = (image = ICON_TYPE.grey) => {
  let size = [23, 26];
  if (image === ICON_TYPE.flag) {
    size = [26, 28];
  }
  return {
    type: 'image',
    image,
    size
  };
};

export const getText = (content: string) => {
  return {
    content, //要展示的文字内容
    direction: 'top', //文字方向，有 icon 时为围绕文字的方向，没有 icon 时，则为相对 position 的位置
    //文字样式
    style: {
      fontSize: 12, //字体大小
      fillColor: '#22886f', //字体颜色
      strokeColor: '#fff', //描边颜色
      strokeWidth: 2 //描边宽度
    }
  };
};
