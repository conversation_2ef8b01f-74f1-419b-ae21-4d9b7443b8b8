import { HitDistanceLibTrainFieldModel } from '@/utils/constants';
import { InfoTypeEnum } from '@/utils/constants';
import { MapTypeEnum } from './constants';

export interface StateModel {
  visible: boolean;
  showFooter: boolean;
  toType?: InfoTypeEnum;
  mapType: MapTypeEnum;
}

export interface OptionsModel {
  name?: string;
  longitude?: number;
  latitude?: number;
  list?: HitDistanceLibTrainFieldModel[];
  relLibId?: number;
  toType?: InfoTypeEnum;
}
