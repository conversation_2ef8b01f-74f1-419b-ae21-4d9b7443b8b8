import { HitDistanceLibTrainFieldModel } from '@/utils/constants';
import { ICON_TYPE, MapTypeEnum } from '@/components/a-map/constants';
import { OptionsModel, StateModel } from '@/components/a-map/types';
import { getIcon, getText } from '@/components/a-map/utils';
import { InfoTypeEnum } from '@/utils/constants';
import { MESSAGE_TYPE, MUtils } from '@paas/paas-library';
import { AmapInit } from 'admin-library';
import { defineComponent, reactive, ref, toRefs } from 'vue';

export default defineComponent({
  props: {
    // 是否可以切换地图类型
    isChangeMapType: {
      type: Boolean,
      default: false
    },
    // 是否展示合并按钮
    isMerge: {
      type: Boolean,
      default: true
    }
  },
  setup(props, { emit }) {
    let AMap = null;
    let mapInstance = null;
    let labelsLayer = null;
    // 周围训练场marker列表
    let markerList = [];
    // 当前选中的训练场id
    let relLibId: number = null;
    let satelliteLayer = null;

    const state = reactive<StateModel>({
      visible: false,
      showFooter: false,
      toType: null,
      mapType: MapTypeEnum.Map_Satellite
    });

    const components = {
      amapCompRef: ref<HTMLDivElement>(null)
    };

    const constants = {
      MapTypeEnum
    };

    const methods = {
      async open(options: OptionsModel = {}) {
        console.log('标记点信息', options);
        state.visible = true;
        await methods.init();

        relLibId = options.relLibId;
        state.toType = options.toType;
        methods.addLayer();

        const { longitude, latitude, list } = options;
        if (longitude && latitude) {
          mapInstance.setCenter(new AMap.LngLat(longitude, latitude));

          const labelMarker = new AMap.LabelMarker({
            name: options.name,
            position: new AMap.LngLat(longitude, latitude),
            zIndex: 11,
            rank: 1,
            icon: getIcon(ICON_TYPE.flag),
            text: getText(options.name)
          });
          labelsLayer.add(labelMarker);
        }

        if (list?.length) {
          state.showFooter = true;
          methods.addMaker(options.list);
        }
        mapInstance.add(labelsLayer);
        mapInstance.setFitView();
      },
      onMerge() {
        if (!relLibId) {
          MUtils.toast(
            `请先选择需要合并的${state.toType === InfoTypeEnum.TRAINING ? '训练场' : '招生点'}`,
            MESSAGE_TYPE.error
          );
          return;
        }
        emit('merge', relLibId);
        methods.close();
      },
      addLayer() {
        labelsLayer = new AMap.LabelsLayer({
          zooms: [4, 20],
          zIndex: 1000,
          collision: false, //该层内标注是否避让
          allowCollision: false //不同标注层之间是否避让
        });
      },
      addMaker(list: HitDistanceLibTrainFieldModel[]) {
        markerList = [];
        list.forEach(item => {
          const labelMarker = new AMap.LabelMarker({
            name: item.name,
            // position: [item.longitude + Math.random(), item.latitude + Math.random()],
            position: [item.longitude, item.latitude],
            zIndex: 11,
            rank: 1, //避让优先级
            icon: getIcon(), //标注图标，将 icon 对象传给 icon 属性
            text: getText(item.name), //标注文本，将 text 对象传给 text 属性
            extData: item
          });
          if (relLibId === item.id) {
            labelMarker.setIcon(getIcon(ICON_TYPE.flag));
          }

          labelMarker.on('click', function () {
            markerList.forEach(maker => {
              maker.setIcon(getIcon());
            });

            let icon;
            if (relLibId === item.id) {
              relLibId = null;
            } else {
              icon = getIcon(ICON_TYPE.flag);
              relLibId = item.id;
            }
            labelMarker.setIcon(icon);
          });

          markerList.push(labelMarker);
        });
        labelsLayer.add(markerList);
      },
      onChangeType() {
        if (state.mapType === MapTypeEnum.Map_Satellite) {
          satelliteLayer.show();
          state.mapType = MapTypeEnum.Map_2D;
        } else {
          satelliteLayer.hide();
          state.mapType = MapTypeEnum.Map_Satellite;
        }
      },
      async init() {
        // 确保之前的实例被清理
        if (mapInstance) {
          methods.reset();
        }

        // 确保地图容器存在
        if (!components.amapCompRef.value) {
          console.warn('地图容器未准备就绪，等待下一个渲染周期');
          await new Promise(resolve => setTimeout(resolve, 100));
          if (!components.amapCompRef.value) {
            console.error('地图容器不存在');
            return;
          }
        }

        AMap = await AmapInit(['AMap.HeatMap', 'AMap.DistrictSearch'], false);
        mapInstance = new AMap.Map(components.amapCompRef.value, {
          resizeEnable: true,
          zoom: 13,
          zooms: [4, 20],
          layers: null
        });

        if (props.isChangeMapType && !satelliteLayer) {
          satelliteLayer = new AMap.TileLayer.Satellite();
          mapInstance.addLayer(satelliteLayer);
          satelliteLayer.hide();
        }
      },
      close() {
        methods.reset();
        state.visible = false;
      },
      reset() {
        mapInstance?.destroy();
        mapInstance = null;
        state.showFooter = false;
        if (satelliteLayer) {
          mapInstance?.removeLayer(satelliteLayer);
          satelliteLayer = null;
        }

        state.mapType = MapTypeEnum.Map_Satellite;
      }
    };

    return {
      ...toRefs(state),
      ...components,
      ...constants,
      ...methods
    };
  }
});
