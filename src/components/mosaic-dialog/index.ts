import { defineComponent, nextTick, reactive, ref, toRefs } from 'vue';
import { MUtils, PaasPostMessage } from '@paas/paas-library';
import Deferred, { DeferredModel } from '@/utils/deferred';
import { APP_SPACE_ID } from '@/utils/constants';

export default defineComponent({
  setup(props, { emit }) {
    let dtd: DeferredModel<{ encodedData: string; previewUrl: string }> = null;

    const state = reactive({
      visible: false,
      loading: false,
      // 当前的图片地址
      url: '',
      // 实际宽度（导出的分辨率）
      canvasWidth: 1000,
      // 实际高度（导出的分辨率）
      canvasHeight: 1000,
      // 是否正在绘制马赛克
      isDrawing: false,
      // 马赛克的块大小
      mosaicSize: 30,
      // 要加载的图像
      img: null,
      // Canvas 2D 上下文
      ctx: null,
      // 操作历史栈，用于保存撤销操作的状态
      history: []
    });

    const constants = {};

    const components = {
      canvasRef: ref<HTMLCanvasElement>(null)
    };

    const methods = {
      open(url: string) {
        state.visible = true;
        state.url = url;

        dtd = Deferred();

        return dtd.promise;
      },
      dialogMounted() {
        methods.loadImage(state.url);
      },
      // 加载图像并绘制到 Canvas 上
      loadImage(url: string) {
        const canvas = components.canvasRef.value;
        state.ctx = canvas.getContext('2d');

        state.img = new Image();
        state.img.crossOrigin = 'anonymous';
        state.img.src = url; // 替换为你的图片 URL

        state.img.onload = async () => {
          state.canvasWidth = state.img.width;
          state.canvasHeight = state.img.height;

          await nextTick();

          // 绘制图像，并保持宽高比不变
          state.ctx.drawImage(state.img, 0, 0, state.img.width, state.img.height);

          // 保存初始状态
          methods.saveState();
        };
      },

      // 保存当前 Canvas 状态到历史栈
      saveState() {
        const imageData = state.ctx.getImageData(0, 0, state.canvasWidth, state.canvasHeight);
        state.history.push(imageData);
      },

      // 撤销上一步操作
      undo() {
        // 确保有可以撤销的历史状态
        if (state.history.length > 1) {
          // 获取上一步的状态
          const previousImageData = state.history[state.history.length - 1];
          // 移除当前状态
          state.history.pop();
          // 恢复 Canvas 到上一步状态
          state.ctx.putImageData(previousImageData, 0, 0);
        }
      },

      // 开始绘制马赛克
      startDrawing() {
        state.isDrawing = true;

        // 在开始绘制之前保存当前状态
        methods.saveState();
      },

      // 停止绘制马赛克
      stopDrawing() {
        state.isDrawing = false;
      },

      // 在 Canvas 上绘制马赛克
      drawMosaic(event: MouseEvent) {
        if (!state.isDrawing) {
          return;
        }

        const rect = components.canvasRef.value.getBoundingClientRect();

        // 计算鼠标在 Canvas 中的实际位置
        // 计算宽度缩放比例
        const scaleX = state.canvasWidth / rect.width;
        // 计算高度缩放比例
        const scaleY = state.canvasHeight / rect.height;
        // 将鼠标 X 坐标映射到实际 Canvas 坐标
        const x = (event.clientX - rect.left) * scaleX;
        // 将鼠标 Y 坐标映射到实际 Canvas 坐标
        const y = (event.clientY - rect.top) * scaleY;

        methods.applyMosaicToArea(x, y);
      },

      // 应用马赛克效果到指定区域
      applyMosaicToArea(x: number, y: number) {
        const { ctx, mosaicSize } = state;

        const startX = Math.floor(x / mosaicSize) * mosaicSize;
        const startY = Math.floor(y / mosaicSize) * mosaicSize;

        const imageData = ctx.getImageData(startX, startY, mosaicSize, mosaicSize);
        const data = imageData.data;

        // 计算马赛克块的平均颜色
        let red = 0,
          green = 0,
          blue = 0,
          count = 0;
        for (let i = 0; i < data.length; i += 4) {
          red += data[i];
          green += data[i + 1];
          blue += data[i + 2];
          count++;
        }

        red = Math.round(red / count);
        green = Math.round(green / count);
        blue = Math.round(blue / count);

        // 设置马赛克块的颜色
        for (let i = 0; i < data.length; i += 4) {
          data[i] = red;
          data[i + 1] = green;
          data[i + 2] = blue;
        }

        ctx.putImageData(imageData, startX, startY);
      },

      dataURLtoFile(dataURL, filename) {
        const arr = dataURL.split(',');
        const mime = arr[0].match(/:(.*?);/)[1];
        const bstr = atob(arr[1]);
        let n = bstr.length;
        const u8arr = new Uint8Array(n);
        while (n--) {
          u8arr[n] = bstr.charCodeAt(n);
        }
        return new File([u8arr], filename, { type: mime });
      },

      // 导出 Canvas 为图片
      exportImage() {
        const canvas = components.canvasRef.value;
        // 将 Canvas 内容转换为图片数据
        const dataURL = canvas.toDataURL('image/png', 1.0);
        const file = methods.dataURLtoFile(dataURL, 'mosaic.png');
        state.loading = true;
        PaasPostMessage.post('base://file.upload', [file], { appSpaceId: APP_SPACE_ID.consumer })
          .then(data => {
            state.loading = false;
            const itemList = data || [];
            const imageData = itemList.find(item => {
              return item.contentType.indexOf('image') > -1;
            });
            if (imageData) {
              emit('success', imageData);
              state.visible = false;
              dtd.resolve(imageData);
            }
          })
          .catch(err => {
            state.loading = false;
            dtd.resolve(null);
            MUtils.toast(err?.message || '上传失败');
          });
      },
      onCancel() {
        dtd.resolve(null);
        dtd = null;
      }
    };

    return {
      ...toRefs(state),
      ...constants,
      ...components,
      ...methods
    };
  }
});
