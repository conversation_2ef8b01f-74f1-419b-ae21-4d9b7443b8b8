<template>
  <pm-dialog v-model:visible="visible" title="图片编辑" width="800" centered @mounted="dialogMounted" @close="onCancel">
    <div class="container">
      <div class="content">
        <canvas
          ref="canvasRef"
          :width="canvasWidth"
          :height="canvasHeight"
          @mousedown="startDrawing"
          @mouseup="stopDrawing"
          @mousemove="drawMosaic"
          style="width: 760px; height: auto"
        ></canvas>
      </div>

      <div class="footer">
        <label for="mosaicSize">马赛克尺寸: {{ mosaicSize }}</label>
        <input class="bar" type="range" id="mosaicSize" min="5" max="50" step="1" v-model="mosaicSize" />
        <m-button @click="undo">撤销</m-button>
      </div>
    </div>

    <template #footer>
      <m-button type="primary" :loading="loading" @click="exportImage">保存</m-button>
    </template>
  </pm-dialog>
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less"></style>
