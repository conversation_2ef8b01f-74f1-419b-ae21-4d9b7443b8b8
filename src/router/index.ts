import { createRouter, createWebHashHistory } from 'vue-router';

const routes = [
  {
    path: '/audit-work-order',
    name: 'audit-work-order',
    component: () => import(`@/application/audit-work-order/index.vue`)
  },
  {
    path: '/jiaxiao-list',
    name: 'jiaxiao-list',
    component: () => import(`@/application/jiaxiao-list/index.vue`)
  },
  {
    path: '/training-ground-list',
    name: 'training-ground-list',
    component: () => import(`@/application/training-ground-list/index.vue`)
  },
  {
    path: '/registration-point-list',
    name: 'registration-point-list',
    component: () => import(`@/application/registration-point-list/index.vue`)
  },
  {
    path: '/examination-ground-list',
    name: 'examination-ground-list',
    component: () => import(`@/application/examination-ground-list/index.vue`)
  },
  {
    path: '/course-list',
    name: 'course-list',
    component: () => import(`@/application/course-list/index.vue`)
  },
  {
    path: '/display-strategy-list',
    name: 'display-strategy-list',
    component: () => import(`@/application/display-strategy-list/index.vue`)
  },
  {
    path: '/blacklist-whitelist-list',
    name: 'blacklist-whitelist-list',
    component: () => import(`@/application/blacklist-whitelist-list/index.vue`)
  }
];

const router = createRouter({
  history: createWebHashHistory(),
  routes
});

export default router;
