<template>
  <pm-app-wrapper>
    <pm-router-view :router="router"></pm-router-view>
    <preview-img />
  </pm-app-wrapper>
</template>
<script lang="ts">
import { defineComponent } from 'vue';
import { useCityDataStore } from '@/pinia/city-data';
import PreviewImg from '@/components/preview-img/index.vue';

import router from './router';

export default defineComponent({
  components: {
    PreviewImg
  },
  setup() {
    // 城市数据
    const cityDataStore = useCityDataStore();

    cityDataStore.fetchCityData();
    return {
      router
    };
  }
});
</script>
<style lang="less">
body {
  background-color: transparent !important;
}
#app {
  position: relative;
  height: 100%;
  background-color: #edf0f4;
  box-sizing: border-box;
}
.app-wrapper {
  height: 100%;
  background: #fff;
  border-radius: 4px;
}
.padding-between {
  .single-item {
    input[type='number']::-webkit-outer-spin-button,
    input[type='number']::-webkit-inner-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }
  }
}
</style>
