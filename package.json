{"name": "mclaren", "version": "1.0.0", "private": true, "scripts": {"serve": "rsbuild dev --mode development --env-mode development", "build:test": "npm run lib:build && rsbuild build --mode production --env-mode test", "build": "npm run lib:build &&  rsbuild build --mode production --env-mode production", "lib:build": "webpack --mode production --config webpack.config.js", "lib:dev": "webpack --mode development --config webpack.config.js", "format": "prettier --write .", "lint:style": "stylelint  --custom-syntax postcss-less  \"./src/*.{css,less,vue}\"", "fix:style": "stylelint  --custom-syntax postcss-less --fix  \"./src/*.{css,less,vue}\""}, "dependencies": {"@ant-design/icons-vue": "6.1.0", "@simplex/simple-base": "6.2.3", "@simplex/simple-core": "4.0.17", "@tanstack/vue-query": "^5.83.0", "admin-library": "^1.0.6", "core-js": "3.23.4", "crypto-js": "^4.2.0", "pinia": "~2.1.7", "vue": "3.4.31", "vue-router": "^4.1.3"}, "devDependencies": {"@babel/plugin-proposal-optional-chaining": "^7.18.9", "@paas/eslint-config-paas": "^2.0.7", "@paas/paas-webpack-plugin": "^1.0.0", "@paas/stylelint-config-paas": "^1.0.4", "@rsbuild/core": "^1.3.20", "@rsbuild/plugin-babel": "^1.0.5", "@rsbuild/plugin-less": "^1.2.4", "@rsbuild/plugin-vue": "^1.0.7", "@rsbuild/plugin-vue-jsx": "^1.1.0", "@rslib/core": "^0.0.10", "@rspack/cli": "^1.4.8", "@rspack/core": "^1.4.6", "@types/fabric": "^5.3.0", "@types/lodash-es": "^4.17.12", "@types/node": "18.0.0", "@vue/eslint-config-typescript": "^13.0.0", "eslint": "^8.56.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "husky": "^4.2.5", "ip": "^1.1.8", "less": "^4.1.3", "less-loader": "^11.0.0", "prettier": "^2.4.1", "typescript": "~5.8.2", "webpack-cli": "^6.0.1"}, "eslintConfig": {"root": true, "env": {"browser": true, "node": true, "es6": true}, "globals": {"APP": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended", "@vue/typescript/recommended", "@paas/eslint-config-paas"], "parserOptions": {"sourceType": "module"}, "plugins": ["prettier"], "rules": {}}, "husky": {"hooks": {"pre-commit": "npm run fix:style"}}, "overrides": {"@paas/paas-library": "^1.2.3-beta-2"}, "browserslist": ["> 1%", "Chrome > 68", "not dead", "not ie 11"]}